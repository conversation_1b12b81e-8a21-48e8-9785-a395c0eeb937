"""
This file contains all the crud operations for 
role related things
"""

from typing import Optional

from app.crud.base import CRUDBase
from app.models.role import Role
from app.schemas.role import RoleCreate, RoleUpdate

from sqlalchemy.orm import Session


class CRUDRole(CRUDBase[Role, RoleCreate, RoleUpdate]):
    def get_by_name(self, db: Session, name: str) -> Optional[Role]:
        try:
            return db.query(Role).filter(Role.name == name).first()
        except:
            return None

    def get_by_id(self, db: Session, id: int) -> Optional[Role]:
        return db.query(Role).filter(Role.id == id).first()

    def get_all_roles(self, db: Session) -> list[Role]:
        return db.query(Role).all()



role = CRUDRole(Role)
