from datetime import datetime, date

from pydantic import BaseModel, EmailStr, constr


class UserBase(BaseModel):
    name: str
    last_name: str = ""
    email: EmailStr
    date_of_birth: date | None = None
    ethnicity: str | None
    cohort_id: int | None = None
    organization_name: str | None  = ""
    role_in_organization: str | None = ""
    organization_type: str | None = ""
    role_id: int | None = None
    role: str | None = None


class UserRoleBase(BaseModel):
    name: str | None  = None
    ethnicity: str | None  = None
    organization_name: str | None  = None
    role_id: int | None = None
    role: str | None = None
    

class UserLogin(BaseModel):
    username: str
    password: str


# User Invite
class UserInvite(BaseModel):
    name: str
    email: EmailStr
    role_id: int
    password: constr(min_length=8)

class UserInviteOut(BaseModel):
    id: int | None = None
    IC: str = ""
    name: str
    role_id: int
    last_name: str | None = ""
    email: EmailStr
    date_of_birth: date | None = None
    ethnicity: str | None
    cohort_id: int | None = None
    organization_name: str | None  = ""
    role_in_organization: str | None = ""
    organization_type: str | None = ""

    class Config:
        orm_mode = True


# Properties to receive via API on Update
class UserUpdate(UserBase):
    password: constr(min_length=8) | None = None


# User DB Representation ()
class UserInDBBase(BaseModel):
    id: int | None = None
    IC: str = ""
    name: str
    role_id: int
    last_name: str = ""
    email: EmailStr
    date_of_birth: date | None = None
    ethnicity: str | None
    cohort_id: int | None = None
    organization_name: str | None  = ""
    role_in_organization: str | None = ""
    organization_type: str | None = ""
    last_login: datetime | None = None
    created_on: datetime | None
    last_updated: datetime | None

    class Config:
        orm_mode = True


class User(UserInDBBase):
    role_name: str | None
    scopes: list[str] | None
    
class UserInDB(UserInDBBase):
    hashed_password: str | None
    

class UserOut(UserInviteOut):
    id: int
    IC: str
    created_on: datetime | None
    last_updated: datetime | None

class UserRoleOut(BaseModel):
    id: int
    name: str
    email: str
    role: str
    
    class Config:
        orm_mode = True

class ForgotPassword(BaseModel):
    email: EmailStr

class ChangePassword(BaseModel):
    old_password: str
    new_password: constr(min_length=8)
    
class UserSearchResponse(BaseModel):
    id: int
    email: str

    class Config:
        orm_mode = True
