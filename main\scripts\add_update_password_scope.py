"""
<PERSON><PERSON><PERSON> to add auth:update_password scope to all roles
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import crud, schemas
from app.db.session import SessionLocal
from app.scopes.auth import AuthScopes
from app.utils.globals import AvailableRoles
from app.logger import logger


def add_update_password_scope_to_all_roles():
    db = SessionLocal()
    try:
        # Define the permission
        permission_name = AuthScopes.update_password
        permission_description = "Permission to update user's password"
        
        # Check if permission already exists
        existing_perm = crud.permission.get_by_name(db=db, name=permission_name)
        if existing_perm:
            logger.info(f"Permission '{permission_name}' already exists")
            permission_id = existing_perm.id
        else:
            # Create new permission
            permission_in = schemas.PermissionCreate(
                name=permission_name,
                description=permission_description
            )
            new_perm = crud.permission.create(db=db, obj_in=permission_in)
            permission_id = new_perm.id
            logger.info(f"Created permission '{permission_name}'")
        
        # Get all roles
        roles = crud.role.get_multi(db=db)
        
        # Assign permission to all roles
        for role in roles:
            # Check if role-permission already exists
            if not crud.role_permission.get_role_permission_by_id(
                db=db, role_id=role.id, permission_id=permission_id
            ):
                role_permission_in = schemas.RolePermissionCreate(
                    role_id=role.id,
                    permission_id=permission_id
                )
                crud.role_permission.create(db=db, obj_in=role_permission_in)
                logger.info(f"Assigned permission '{permission_name}' to role '{role.name}'")
            else:
                logger.info(f"Permission '{permission_name}' already assigned to role '{role.name}'")
        
        logger.info("Update password permission setup completed successfully")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    add_update_password_scope_to_all_roles()