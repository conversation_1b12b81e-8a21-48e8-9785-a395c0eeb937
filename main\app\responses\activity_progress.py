from pydantic import BaseModel


class ActivityProgressResponsesClass(BaseModel):

    CREATE_SUCCESS: dict = {
        "success": True,
        "message": "Activity Progress added successfully.",
    }

    # CREATE_ACTIVITY_EXISTS: dict = {
    #     "success": False,
    #     "message": "Activity already exists.",
    # }

    CREATE_FAILED: dict = {
        "success": False,
        "message": "Failed to add activity progress.",
    }

    INVALID_COURSE_ID: dict = {
        "success": False,
        "message": "Invalid course id.",
    }

    NOT_ENROLLED: dict = {
        "success": False,
        "message": "You are not enrolled in this course.",
    }

    INVALID_ACTIVITY_ID: dict = {
        "success": False,
        "message": "Invalid activity id.",
    }

    UPDATE_FAILED: dict = {
        "success": False,
        "message": "Failed to update.",
    }

    NOT_EXIST: dict = {
        "success": True,
        "message": "No activity found.",
    }

    DELETED_SUCCESS: dict = {
        "success": True,
        "message": "Activity deleted successfully.",
    }
    
    UPDATED_SUCCESS: dict = {
        "success": True,
        "message": "Activity Progress updated successfully.",
    }

    DELETE_FAILED: dict = {
        "success": False,
        "message": "Failed to delete activity progress.",
    }

    INVALID_JSON: dict = {
        "success": False,
        "message": "Invalid json for user input.",
    }

ActivityProgressResponses = ActivityProgressResponsesClass()
