"""
This file contains all the API's related to
auth
"""

from app import schemas, crud, models
from app.core.security import verify_password
from app.api import deps
from app.core.auth import authenticate, create_jwt_token
from app.responses.base import APIResponse
from app.responses import AuthResponses,GeneralResponses
from app.responses.base import APIResponse, APIException
from app.utils.globals import AvailableRoles
from app.logger import logger
from app.utils.globals import create_info_logging_message
from app.models.organization import OrganizationStatus

from fastapi import APIRouter, Depends, APIRouter, status
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session


router = APIRouter()



@router.post("/swagger_ui", response_model=schemas.LoginDataObj)
def admin_login(
    form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(deps.get_db)
):
    """
    Using FastAPi in Swagger UI this endpoint will be use as that depends on OAuth2PasswordRequestForm shape
    :param form_data:
    :param db:
    :return: access token for login user
    """
    user = authenticate(
        username=form_data.username,
        db=db,
    )
    if not user or not verify_password(form_data.password, user.hashed_password):
        return JSONResponse(
            status_code=401,
            content=APIResponse(**AuthResponses.AUTH_LOGIN_INVALID_CREDENTIALS),
        )

    sub = user.id
    # Fetch user permissions and add them to token
    scopes = deps.get_user_scopes(db=db, user=user)
    access_token, refresh_token = create_jwt_token(
        data={"sub": sub, "scopes": scopes}
    )

    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/login", response_model=schemas.AuthLoginOut)
def auth_login(
    form_data: schemas.AuthLogin,
    db: Session = Depends(deps.get_db),
) -> JSONResponse:
    """Login a new incoming user \n

    Args:
        form_data
        source
        db
    """

    try:
        user = authenticate(
            username=form_data.username,
            db=db,
        )

        if not user:
            logger.info(
                create_info_logging_message(
                    endpoint="/login",
                    feature="auth",
                    message=str(AuthResponses.AUTH_SIGNUP_USER_NOT_FOUND["message"]),
                )
            )
            return JSONResponse(
                status_code=401,
                content=APIResponse(**AuthResponses.AUTH_SIGNUP_USER_NOT_FOUND),
            )

        # Check if user belongs to an organization and if that organization is on hold
        user_role = crud.role.get(db=db, id=user.role_id).name

        # Only check organization status for non-super-admin users
        if user_role != AvailableRoles.super_admin and user.organization_id:
            # Get the organization
            organization = db.query(models.Organization).filter(models.Organization.id == user.organization_id).first()
            if organization and organization.status == OrganizationStatus.HOLD:
                logger.info(
                    create_info_logging_message(
                        endpoint="/login",
                        feature="auth",
                        message=f"Login denied for user {user.email} - organization {organization.name} is on hold",
                    )
                )
                return JSONResponse(
                    status_code=403,
                    content=APIResponse(
                        success=False,
                        message="Your organization account is currently on hold. Please contact the administrator.",
                    ),
                )

       # Store user id to fetch users info when decoding the jwt
        sub = user.id

        # it contains the redirect page endpoint during the login process
        # which basically helps and tell the user where to redirect
        # either admin, expert, or agent
        if user_role == AvailableRoles.admin:
            redirect_page = "/admin"
        elif user_role == AvailableRoles.instructor:
            redirect_page = "/instructor"
        elif user_role == AvailableRoles.learner:
            redirect_page = "/learner"
        elif user_role == AvailableRoles.super_admin:
            redirect_page = "/superadmin"
        else:
            redirect_page = ""


        # verify incoming password with fetched user password
        if not verify_password(form_data.password, user.hashed_password):
            logger.info(
                create_info_logging_message(
                    endpoint="/login",
                    feature="auth",
                    message=str(AuthResponses.AUTH_LOGIN_INVALID_CREDENTIALS["message"]),
                )
            )
            return JSONResponse(
                status_code=401,
                content=APIResponse(**AuthResponses.AUTH_LOGIN_INVALID_CREDENTIALS),
            )

        # update last login
        crud.user.update_last_login(db=db, db_obj=user)

        # Fetch user permissions and add them to token
        scopes = deps.get_user_scopes(db=db, user=user)


        # this if else to handle breaking part on prod
        access_token, refresh_token = create_jwt_token(
                data={"sub": sub, "scopes": scopes}
            )

        logger.info(
            create_info_logging_message(
                endpoint="/login",
                feature="auth",
                message=str(AuthResponses.AUTH_LOGIN_SUCCESS["message"] + user.name + " logged in" + str(user.role_id) + " " + user_role),
            )
        )

        # Update last login depending on user
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **AuthResponses.AUTH_LOGIN_SUCCESS,
                data={
                    "access_token": access_token,
                    "token_type": "bearer",
                    "refresh_token": refresh_token,
                    "redirect_page": redirect_page,
                    "user_data": {
                        "id": user.id,
                        "name": user.name,
                        "email": user.email,
                        "role": user.role_id,
                    }
                },
            ),
        )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/refresh", response_model=schemas.AuthRefresh)
def auth_refresh(
    refresh_token: str,
    db: Session = Depends(deps.get_db),
) -> JSONResponse:
    """Refresh a JWT token

    Args:
        refresh_token
        db (Session, optional): Database session. Defaults to Depends(deps.get_db).

    Returns:
        JSONResponse: JSONResponse containing JWT access and refresh token
    """

    try:
        # Decode and get token information
        token_data = deps.decode_jwt_token(refresh_token)
        if not token_data:
            return JSONResponse(
                status_code=401,
                content=APIResponse(**AuthResponses.AUTH_REFRESH_TOKEN_INVALID),
            )
        if token_data.token_type != "refresh":
            return JSONResponse(
                status_code=401,
                content=APIResponse(**AuthResponses.AUTH_REFRESH_TOKEN_INVALID),
            )
        # Get user from database
        user = crud.user.get(db=db, id=token_data.username)

        # Check if user belongs to an organization and if that organization is on hold
        user_role = crud.role.get(db=db, id=user.role_id).name

        # Only check organization status for non-super-admin users
        if user_role != AvailableRoles.super_admin and user.organization_id:
            # Get the organization
            organization = db.query(models.Organization).filter(models.Organization.id == user.organization_id).first()
            if organization and organization.status == OrganizationStatus.HOLD:
                logger.info(
                    create_info_logging_message(
                        endpoint="/refresh",
                        feature="auth",
                        message=f"Token refresh denied for user {user.email} - organization {organization.name} is on hold",
                    )
                )
                return JSONResponse(
                    status_code=403,
                    content=APIResponse(
                        success=False,
                        message="Your organization account is currently on hold. Please contact the administrator.",
                    ),
                )

        # Fetch user permissions and add them to token
        scopes = deps.get_user_scopes(db=db, user=user)

        # Create new access token
        access_token, refresh_token = create_jwt_token(
            data={
                "sub": token_data.username,
                "scopes": scopes,
            }
        )

        # Update last login depending on user
        crud.user.update_last_login(db=db, db_obj=user)

        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **AuthResponses.AUTH_REFRESH_SUCCESS,
                data={
                    "access_token": access_token,
                    "token_type": "bearer",
                    "refresh_token": refresh_token,
                    "otp_required": False,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


