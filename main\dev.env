# Environment
APP_ENVIRONMENT="development"
# Email Settings
EMAIL_FROM_EMAIL="<EMAIL>"
EMAIL_FROM_NAME="No-Reply Mandai"
EMAIL_SMTP_HOST="smtp.gmail.com"
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME="<EMAIL>"
EMAIL_SMTP_PASSWORD="dujittwvobvwxktm"
EMAIL_SMTP_TLS=True
EMAIL_SMTP_SSL=True
# swagger username & paasowrd
SWAGGER_USERNAME="IAL-dev"
SWAGGER_PASSWORD="ialDev"
# IAL Database Credentials local
DATABASE_CONNECTION_URL="postgresql://{DATABASE_USERNAME}:{DATABASE_PASSWORD}@{DATABASE_HOST}:{DATABASE_PORT}/{DATABASE_NAME}"
DATABASE_HOST="localhost"
DATABASE_PORT="5432"
DATABASE_USERNAME="postgres"
DATABASE_PASSWORD="abc"
DATABASE_NAME="ial_db"
# JWT Authorization Variables
JWT_SECRET_KEY="eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6MTcxMzAzNzA1NywiaWF0IjoxNzEzMDM3MDU3fQ.ki25CwlBoUhjkMMGs6X-y1xrbN_715Fb2f35C5x5lHs"
JWT_ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRY_MINUTES=10080
REFRESH_TOKEN_EXPIRY_MINUTES=10080
