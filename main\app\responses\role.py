"""
This file contains all the responses 
of Roles related API's
"""

from pydantic import BaseModel


class RoleResponsesClass(BaseModel):
    ROLE_CREATE_SUCCESS: dict = {
        "success": True,
        "message": "Role created successfully.",
    }

    ROLE_UPDATE_SUCCESS: dict = {
        "success": True,
        "message": "Role updated successfully.",
    }

    ROLE_CREATE_NOT_ALLOWED: dict = {
        "success": False,
        "message": "You are not allowed to create Role.",
    }

    ROLE_DELETE_SUCCESS: dict = {
        "success": True,
        "message": "Role deleted successfully.",
    }

    ALL_ROLE: dict = {
        "success": True,
        "message": "All Roles.",
    }
    
    ROLE_GET_SUCCESS: dict = {
        "success": True,
        "message": "Role fetched successfully.",
    }
    
    INVALID_ROLE: dict = {
        "success": True,
        "message": "Invalid Role.",
    }

    


RoleResponses = RoleResponsesClass()
