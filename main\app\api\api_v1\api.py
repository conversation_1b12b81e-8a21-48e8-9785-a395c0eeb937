from app.api.api_v1.endpoints import (
    auth,
    user,
    role,
    permission,
    role_permission,
    organization,
    course,
    course_progress,
    activity,
    activity_progress,
    ai_conversation,
    analytics,
    cohort,
    heygen,
    heygen_video,
    simulation
)
from fastapi import APIRouter


# Create a new main APIRouter
api_router = APIRouter()

# Add all endpoints to the API Router
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(user.router, prefix="/user", tags=["user"])
api_router.include_router(organization.router, prefix="/organization", tags=["organization"])
api_router.include_router(cohort.router, prefix="/cohort", tags=["cohort"])
api_router.include_router(role_permission.router, prefix="/role_permission", tags=["role_permission"])
api_router.include_router(role.router, prefix="/role", tags=["role"])
api_router.include_router(permission.router, prefix="/permission", tags=["permission"])
api_router.include_router(course.router, prefix="/course", tags=["course"])
api_router.include_router(course_progress.router, prefix="/course_progress", tags=["course_progress"])
api_router.include_router(activity.router, prefix="/activity", tags=["activity"])
api_router.include_router(activity_progress.router, prefix="/activity-progress", tags=["activity progress"])
api_router.include_router(ai_conversation.router, prefix="/ai-conversation", tags=["chat with bot"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["Analytics"])
api_router.include_router(heygen.router, prefix="/heygen", tags=["heygen"])
api_router.include_router(heygen_video.router, prefix="/heygen-video", tags=["heygen-video"])
api_router.include_router(simulation.router, prefix="/simulation", tags=["simulation"])

