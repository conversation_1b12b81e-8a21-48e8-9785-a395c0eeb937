"""My awesome migrations

Revision ID: ac8d0c4fb117
Revises: 
Create Date: 2024-07-23 22:06:25.867840

"""
from alembic import op
import sqlalchemy as sa
import sqlalchemy_utils


# revision identifiers, used by Alembic.
revision = 'ac8d0c4fb117'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True, comment='activity name'),
    sa.Column('type', sa.Enum('pre_course_survey', 'quiz', 'tutorial', 'hardware_test', 'video_example', 'plunge_activity', 'practice_activity', 'assessment', 'boss_challenge', 'recap', 'feedback', name='activitytype'), nullable=True),
    sa.Column('questions', sa.JSON(), nullable=True),
    sa.Column('video', sa.String(), nullable=True, comment='path of video file if there is a video attached with activity'),
    sa.Column('retries_allowed', sa.Integer(), nullable=True),
    sa.Column('extra_fields', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_activity_id'), 'activity', ['id'], unique=False)
    op.create_table('cohort',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=40), nullable=True, comment='cohort name'),
    sa.Column('file_hash', sa.String(length=100), nullable=True, comment='user name'),
    sa.Column('file_path', sa.String(length=200), nullable=True, comment='user file path'),
    sa.Column('created_on', sa.DateTime(), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('file_hash')
    )
    op.create_index(op.f('ix_cohort_id'), 'cohort', ['id'], unique=False)
    op.create_table('permission',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('created_on', sa.DateTime(), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_permission_id'), 'permission', ['id'], unique=False)
    op.create_table('role',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('created_on', sa.DateTime(), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_role_id'), 'role', ['id'], unique=False)
    op.create_table('m_c_q_s',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('activity_id', sa.Integer(), nullable=True),
    sa.Column('question', sa.String(), nullable=True, comment='question text'),
    sa.Column('options', sa.JSON(), nullable=True),
    sa.Column('right_option', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['activity_id'], ['activity.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_m_c_q_s_id'), 'm_c_q_s', ['id'], unique=False)
    op.create_table('role_permission',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.Column('permission_id', sa.Integer(), nullable=True),
    sa.Column('created_on', sa.DateTime(), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permission.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['role.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('role_id', 'permission_id', name='role_permission_uc')
    )
    op.create_index(op.f('ix_role_permission_id'), 'role_permission', ['id'], unique=False)
    op.create_table('user',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=40), nullable=False, comment='user name'),
    sa.Column('IC', sa.String(length=40), nullable=False, comment='Identity Card Number'),
    sa.Column('email', sqlalchemy_utils.types.email.EmailType(length=255), nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('cohort_id', sa.Integer(), nullable=True),
    sa.Column('ethnicity', sa.String(length=40), nullable=False, comment='Race of user'),
    sa.Column('profile_picture', sa.String(length=300), nullable=True, comment='profile picture file path'),
    sa.Column('hashed_password', sa.String(length=256), nullable=False),
    sa.Column('timezone', sa.String(length=200), nullable=True),
    sa.Column('language', sa.String(length=100), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('created_on', sa.DateTime(), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['cohort_id'], ['cohort.id'], ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['role_id'], ['role.id'], ondelete='RESTRICT'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_cohort_id'), 'user', ['cohort_id'], unique=False)
    op.create_index(op.f('ix_user_id'), 'user', ['id'], unique=False)
    op.create_index(op.f('ix_user_role_id'), 'user', ['role_id'], unique=False)
    op.create_table('course',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=80), nullable=True, comment='course name'),
    sa.Column('instructor', sa.Integer(), nullable=True),
    sa.Column('description', sa.String(length=500), nullable=True, comment='course description'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['instructor'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_course_id'), 'course', ['id'], unique=False)
    op.create_table('activity_progress',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('course_id', sa.Integer(), nullable=False),
    sa.Column('activity_id', sa.Integer(), nullable=False),
    sa.Column('user_input', sa.JSON(), nullable=True),
    sa.Column('result', sa.JSON(), nullable=True),
    sa.Column('retries', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['activity_id'], ['activity.id'], ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='RESTRICT'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_activity_progress_activity_id'), 'activity_progress', ['activity_id'], unique=False)
    op.create_index(op.f('ix_activity_progress_course_id'), 'activity_progress', ['course_id'], unique=False)
    op.create_index(op.f('ix_activity_progress_id'), 'activity_progress', ['id'], unique=False)
    op.create_index(op.f('ix_activity_progress_user_id'), 'activity_progress', ['user_id'], unique=False)
    op.create_table('conversation',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('course_id', sa.Integer(), nullable=True),
    sa.Column('activity_id', sa.Integer(), nullable=True),
    sa.Column('prompts_count', sa.Integer(), nullable=True),
    sa.Column('screen', sa.String(length=40), nullable=True, comment='Screen name from where conversation is started'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['activity_id'], ['activity.id'], ),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversation_id'), 'conversation', ['id'], unique=False)
    op.create_table('course_activity_association',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('course_id', sa.Integer(), nullable=True),
    sa.Column('activity_id', sa.Integer(), nullable=True),
    sa.Column('sequence', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['activity_id'], ['activity.id'], ),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_course_activity_association_id'), 'course_activity_association', ['id'], unique=False)
    op.create_table('pre_course',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('course_id', sa.Integer(), nullable=True),
    sa.Column('question', sa.String(length=400), nullable=False, comment='question text'),
    sa.Column('created_on', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_pre_course_id'), 'pre_course', ['id'], unique=False)
    op.create_table('course_progress',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('course_id', sa.Integer(), nullable=False),
    sa.Column('last_activity_progress', sa.Integer(), nullable=True),
    sa.Column('progress_percentage', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['last_activity_progress'], ['activity_progress.id'], ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='RESTRICT'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_course_progress_course_id'), 'course_progress', ['course_id'], unique=False)
    op.create_index(op.f('ix_course_progress_id'), 'course_progress', ['id'], unique=False)
    op.create_index(op.f('ix_course_progress_user_id'), 'course_progress', ['user_id'], unique=False)
    op.create_table('message',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('conversation_id', sa.Integer(), nullable=True),
    sa.Column('message', sa.String(), nullable=True, comment='query from user'),
    sa.Column('sender', sa.String(length=40), nullable=True, comment='sender (user or chatbot)'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversation.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_message_id'), 'message', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_message_id'), table_name='message')
    op.drop_table('message')
    op.drop_index(op.f('ix_course_progress_user_id'), table_name='course_progress')
    op.drop_index(op.f('ix_course_progress_id'), table_name='course_progress')
    op.drop_index(op.f('ix_course_progress_course_id'), table_name='course_progress')
    op.drop_table('course_progress')
    op.drop_index(op.f('ix_pre_course_id'), table_name='pre_course')
    op.drop_table('pre_course')
    op.drop_index(op.f('ix_course_activity_association_id'), table_name='course_activity_association')
    op.drop_table('course_activity_association')
    op.drop_index(op.f('ix_conversation_id'), table_name='conversation')
    op.drop_table('conversation')
    op.drop_index(op.f('ix_activity_progress_user_id'), table_name='activity_progress')
    op.drop_index(op.f('ix_activity_progress_id'), table_name='activity_progress')
    op.drop_index(op.f('ix_activity_progress_course_id'), table_name='activity_progress')
    op.drop_index(op.f('ix_activity_progress_activity_id'), table_name='activity_progress')
    op.drop_table('activity_progress')
    op.drop_index(op.f('ix_course_id'), table_name='course')
    op.drop_table('course')
    op.drop_index(op.f('ix_user_role_id'), table_name='user')
    op.drop_index(op.f('ix_user_id'), table_name='user')
    op.drop_index(op.f('ix_user_cohort_id'), table_name='user')
    op.drop_table('user')
    op.drop_index(op.f('ix_role_permission_id'), table_name='role_permission')
    op.drop_table('role_permission')
    op.drop_index(op.f('ix_m_c_q_s_id'), table_name='m_c_q_s')
    op.drop_table('m_c_q_s')
    op.drop_index(op.f('ix_role_id'), table_name='role')
    op.drop_table('role')
    op.drop_index(op.f('ix_permission_id'), table_name='permission')
    op.drop_table('permission')
    op.drop_index(op.f('ix_cohort_id'), table_name='cohort')
    op.drop_table('cohort')
    op.drop_index(op.f('ix_activity_id'), table_name='activity')
    op.drop_table('activity')
    # ### end Alembic commands ###
