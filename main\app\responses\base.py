class APIResponse(dict):
    def __init__(self, success, message, data=None):
        self.success = success
        self.message = message
        try:
            self.data = data.dict()
        except AttributeError:
            self.data = data
        dict.__init__(self, **self.dict())

    def dict(self):
        if self.data:
            return {
                "success": self.success,
                "message": self.message,
                "data": self.data,
            }
        return {
            "success": self.success,
            "message": self.message,
        }


class APIException(Exception):
    def __init__(self, message: dict, status_code: int, headers: dict = {}):
        self.message = message
        self.status_code = status_code
        self.headers = headers
