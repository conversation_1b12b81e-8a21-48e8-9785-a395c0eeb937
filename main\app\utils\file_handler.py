from botocore.exceptions import NoCredentialsError
import boto3
import os
from dotenv import load_dotenv

load_dotenv()

AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_BUCKET_NAME = os.getenv('AWS_BUCKET_NAME')

s3_client = boto3.client(
    's3',
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY
)

def upload_file_to_s3(folder, file, make_public=False):
    try:
        # Set the folder path in S3
        s3_key = f"{folder}/{file.filename}"

        # Set extra args for upload
        extra_args = {}

        # Set content type based on file extension
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension in ['.mp4', '.mov', '.avi', '.webm']:
            extra_args['ContentType'] = f'video/{file_extension[1:]}'

        # Upload the file to S3 with extra args
        s3_client.upload_fileobj(
            file.file,
            AWS_BUCKET_NAME,
            s3_key,
            ExtraArgs=extra_args
        )

        # For backward compatibility, if make_public is True, generate a presigned URL
        # instead of returning a public URL
        if make_public:
            presigned_url = generate_presigned_url(s3_key, expiration=3600, check_public=False)
            return s3_key, presigned_url

        return s3_key

    except NoCredentialsError:
        raise NoCredentialsError
    except Exception as e:
        print(f"Error uploading file to S3: {str(e)}")
        # If an error occurs during upload, return just the key
        # or generate a presigned URL if make_public is True
        if make_public and 's3_key' in locals():
            presigned_url = generate_presigned_url(s3_key, expiration=3600, check_public=False)
            return s3_key, presigned_url
        return s3_key if 's3_key' in locals() else None


def generate_presigned_url(s3_key, expiration: int = 3600, check_public=False):
    """
    Generate a presigned URL for accessing an S3 object.

    Args:
        s3_key (str): The S3 key (path) of the object
        expiration (int): Expiration time in seconds (default: 1 hour)
        check_public (bool): Legacy parameter, no longer used but kept for backward compatibility

    Returns:
        str: A presigned URL for the S3 object
    """
    try:
        # Always generate a pre-signed URL for the S3 object
        # This is the secure way to provide temporary access to private objects
        presigned_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': AWS_BUCKET_NAME, 'Key': s3_key},
            ExpiresIn=expiration
        )

        return presigned_url

    except NoCredentialsError:
        raise NoCredentialsError
    except Exception as e:
        print(f"Error generating presigned URL: {str(e)}")
        # In case of error, return None instead of a direct URL
        # This prevents exposing potentially inaccessible URLs
        return None