"""
<PERSON><PERSON><PERSON> to add HeygenVideo scopes to the database
"""

from app import crud, schemas
from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.scopes.heygen_video import HeygenVideoScopes
from app.utils.globals import AvailableRoles
from app.logger import logger


def add_heygen_video_scopes():
    db = SessionLocal()
    try:
        # Define all HeygenVideo scopes
        scopes = [
            {"name": HeygenVideoScopes.create, "description": "Permission to create Heygen videos"},
            {"name": HeygenVideoScopes.read, "description": "Permission to read Heygen video details"},
            {"name": HeygenVideoScopes.update, "description": "Permission to update Heygen video details"},
            {"name": HeygenVideoScopes.delete, "description": "Permission to delete Heygen videos"},
            {"name": HeygenVideoScopes.use_in_activity, "description": "Permission to use Heygen videos in activities"}
        ]
        
        # Create scopes and store their IDs
        scope_ids = {}
        for scope in scopes:
            # Check if scope already exists
            existing_scope = crud.permission.get_by_name(db=db, name=scope["name"])
            if existing_scope:
                print(f"Scope '{scope['name']}' already exists")
                scope_ids[scope["name"]] = existing_scope.id
            else:
                # Create new scope
                scope_in = schemas.PermissionCreate(
                    name=scope["name"],
                    description=scope["description"]
                )
                new_scope = crud.permission.create(db=db, obj_in=scope_in)
                scope_ids[scope["name"]] = new_scope.id
                print(f"Created scope '{scope['name']}'")
        
        # Get the instructor role
        instructor_role = crud.role.get_by_name(db=db, name=AvailableRoles.instructor)
        if not instructor_role:
            print(f"Role '{AvailableRoles.instructor}' not found")
            return
        
        # Assign scopes to instructor role
        for scope_name, scope_id in scope_ids.items():
            # Check if role-scope already exists
            if not crud.role_permission.get_role_permission_by_id(
                db=db, role_id=instructor_role.id, permission_id=scope_id
            ):
                role_permission_in = schemas.RolePermissionCreate(
                    role_id=instructor_role.id,
                    permission_id=scope_id
                )
                crud.role_permission.create(db=db, obj_in=role_permission_in)
                print(f"Assigned scope '{scope_name}' to role '{AvailableRoles.instructor}'")
            else:
                print(f"Scope '{scope_name}' already assigned to role '{AvailableRoles.instructor}'")
        
        print("HeygenVideo scopes setup completed successfully")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    add_heygen_video_scopes()
