from datetime import datetime

from pydantic import BaseModel
from typing import List


class ActivityProgressCreate(BaseModel):
    course_id: int
    activity_id: int
    user_input: List[dict]
    result: dict
    
    
# class ActivityProgressDemo(BaseModel):
#     activity_type: str = Form(...)
    
class ActivityProgressInDB(ActivityProgressCreate):
    user_id: int
    created_at: datetime | None
    
class ActivityProgressUpdate(BaseModel):
    user_input: List[dict]
    result: dict
    
class ActivityProgressReturn(ActivityProgressInDB):
    id: int | None = None
    
    class Config:
        orm_mode = True
    
class ActivityProgressResult(BaseModel):
    id: int
    user_input: List[dict] | None = [{}]
    result: dict | None = {}
    
    class Config:
        orm_mode = True
    
class ActivityProgressCohortResult(BaseModel):
    result: dict | None = {}