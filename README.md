# IAL Backend

The IAL backend interfaces with frontend for all functional workflows, such as account creation, user management, animal management, animal portfolios, etc. This README file contains general instructions on how to prepare environment and install necessary packages and dependencies to run the project.

## **Directory Structure**

The project directory structure for this application has the following directories and files:

### **Alembic directory for database migrations (main/alembic)**

- Contains configuration in main/alembic/env.py
- Updates database tables directly via code
- Maintains previous migrations in main/alembic/versions/ folder.

### **FastAPI Application Resources in `main/app`**

| File/Directory                                         | Purpose                                                                                                                                                                                                                                                                                                   |
| ------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [`api/`](./main/app/api/README.md)             | Contains different versions of the app (e.g. `app/api/v1`, `app/api/v2`) to support URL versioning, each with its own endpoint directory, containing APIs for each feature.(e.g. role.py for role feature API's)                                                                                      |
| [`core/`](./main/app/core/README.md)           | Holds all the core implementations (e.g. send email to user in `user.py`, configure database in `config.py`, get `scopes/`permissions in `scopes.py`, authenticate the user & creating jwt & security.py to verify password and password hashing) in `auth.py`. |
| [`crud/`](./main/app/crud/README.md)           | Allows for reading, filtering, inserting, updating, and deleting data (e.g. `crud_role.py` for role feature).                                                                                                                                                                                         |
| [`db/`](./main/app/db/README.md)               | Manages database initialization, connections, migrations, health check, and sessions.                                                                                                                                                                                                                     |
| [`models/`](./main/app/models/README.md)       | Defines all database tables (e.g. `role.py` at app/models/ contains all the columns of role table).                                                                                                                                                                                                   |
| [`responses/`](./main/app/responses/README.md) | Holds all API responses and error codes (e.g. `role.py` for role feature).                                                                                                                                                                                                                            |
| [`schemas/`](./main/app/schemas/README.md)     | Contains Pydantic validation classes for models, to automatically validate incoming API requests (e.g. `role.py` for role feature).                                                                                                                                                                   |
| [`scopes/`](./main/app/scopes/README.md)       | Defines the access types for each feature (e.g. `role.py` for role feature).                                                                                                                                                                                                                          |
| [`services/`](./main/app/services/README.md)   | Integrates third-party services like email.                                                                                                                                                                                                                                              |
| [`utils/`](./main/app/utils/README.md)         | Contains global constants, enums, and functions.                                                                                                                                                                                                                                                          |
| [`logging.conf`](./main/app/logging.conf)      | Contains basic configurations for logging.                                                                                                                                                                                                                                                                |
| [`main.py`](./main/app/main.py)                | Entry point of the project, contains server configurationsto run it.                                                                                                                                                                                                                                      |

### **Other Application Resources**

| File/Directory                                                 | Purpose                                                               |
| -------------------------------------------------------------- | --------------------------------------------------------------------- |
| [`main/dev.env`](./main/dev.env)                         | Holds environment variable definitions.                               |
| [`main/pyproject.toml`](./main/pyproject.toml) | Lists required libraries and tools for the project, with the version. |

## Preparing Environment

The steps below were performed on Ubuntu 20.04.4 LTS running on Windows Subsystem for Linux 2 (WSL2) / Windows 11 21H2.
Steps might vary on other platforms.


### Installing Python 3.10

Please visit this link to get the Python 3.10 package for your distribution. On Ubuntu, you can however, use
_deadsnakes_ repository to install the required version.

- Add PPA repository to your system.

```shell
sudo add-apt-repository ppa:deadsnakes/ppa
```

- Update apt cache to fetch new packages information.

```shell
sudo apt update
```

- Install Python and the required development headers

```shell
sudo apt install python3.10 python3.10-dev
```

- Download _get-pip.py_ file and install pip.

```shell
curl https://bootstrap.pypa.io/get-pip.py | sudo python3.10
```


### Installing and Configuring Poetry

The project uses Poetry https://python-poetry.org/ as the Python package and dependency
manager . Execute the following command to install Poetry.

```shell
python3.10 -m pip install poetry
```

By default, Poetry creates virtual environments in your home directory. To override this
default behaviour and set Poetry to create environments inside the project folder itself,
run the following command:

```shell
poetry config virtualenvs.in-project true
```

**Note**: You might need to append _"python 3.10 -m"_ before the command if the shell is
unable to find the _poetry_ command.

- Change directory to the /main.
- Configure poetry to use _python 3.10_ and create the initial environment.

```shell
poetry env use $(which python3.10)
```

- Install the required dependencies from _pyproject.toml_ file.

```shell
poetry install
```

- Activate the new environment

```shell
source .venv/bin/activate
```

### Setup Environment Variables

To set the variables in virtual environment, add the below line in the top of your <name_of_your_virtual_enironment>/bin/activate.sh file.

**Note:** Make sure your file should be placed where your <name_of_your_virtual_enironment> directory is.

```shell
export $(grep -v '^#' dev.env | xargs)
```

For windows add below at <name_of_your_virtual_enironment>/Script/activate.bat:

```bat
for /f "tokens=1,* delims==" %%a in ('type .\dev.env ^| findstr /v "^#"') do (
    setx %%a %%b > nul
    if errorlevel 1 (
        echo Invalid line: %%a=%%b
    )
)
```

## Running the Project

On development environment, you can use the provided _main.py_ file to run the project.
It sets logging level to debug, and enables reload. Inside the file, change the
_host_ and _port_ variables as required.

```python
if __name__ == "__main__":
    # Use this for debugging purposes only
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8787, log_level="debug", reload=True)
```

To run the project, execute the following command.

```shell
python main.py
```

## Database Migrations

The project uses Alembic to perform migrations to the database, such as adding/deleting tables, columns, etc.
Alembic configurations can be found in `main/alembic`. The `env.py` file contains reference to our `Base` models and
database connection URL. Migrations version history is in `main/alembic/versions`.

To generate a new migration version:

```shell
poetry run alembic revision --autogenerate -m "My awesome migrations"
```

**Important NOTE:** Check migration file located at 'main/alembic/versions/' and see in that file that 'sqlalchemy_utils' library is being used or not. If it is being used, there could be scenario that it is not imported. You have to manually add below line in alembic migration file located at 'main/alembic/versions/' for it.

```shell
import sqlalchemy_utils
```

To apply the latest migration:

```shell
poetry run alembic upgrade head
```

Alembic documentation can be found here: https://alembic.sqlalchemy.org/en/latest/index.html

