"""
This file contains all the responses 
of Roles & Permissions Module
"""

from pydantic import BaseModel


class RolePermissionResponsesClass(BaseModel):
    ROLE_PERMISSION_CREATE_SUCCESS: dict = {
        "success": True,
        "message": "Role & Permission created successfully.",
    }

    ROLE_PERMISSION_UPDATE_SUCCESS: dict = {
        "success": True,
        "message": "Role & Permission updated successfully.",
    }

    ROLE_PERMISSION_CREATE_NOT_ALLOWED: dict = {
        "success": False,
        "message": "You are not allowed to create Role & Permission",
    }

    ROLE_PERMISSION_DELETE_SUCCESS: dict = {
        "success": True,
        "message": "Role & Permission deleted successfully.",
    }

    ROLE_PERMISSION_GET_SUCCESS: dict = {
        "success": True,
        "message": "Role & Permission fetched successfully.",
    }

    ALL_ROLE_PERMISSION: dict = {
        "success": True,
        "message": "All Roles & Permissions.",
    }

    ROLE_PERMISSION_EXISTS: dict = {
        "success": False,
        "message": "Role & Permission already exits.",
    }

    ROLE_PERMISSION_FORBIDDEN: dict = {
        "success": False,
        "message": "You are not allowed to access this Role & Permission.",
    }

    ROLE_PERMISSION_NOT_FOUND: dict = {
        "success": False,
        "message": "Role & Permission not exits.",
    }


RolePermissionResponses = RolePermissionResponsesClass()
