"""Add organization table and update user table

Revision ID: a1f2625dcfec
Revises: 1571bc56ad6d
Create Date: 2025-05-08 00:48:58.543964

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1f2625dcfec'
down_revision = '1571bc56ad6d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('organization',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='organization name'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='organization description'),
    sa.Column('status', sa.Enum('APPROVED', 'HOLD', 'PENDING', name='organizationstatus'), nullable=False, comment='organization status'),
    sa.Column('address', sa.String(length=200), nullable=True, comment='organization address'),
    sa.Column('contact_email', sa.String(length=100), nullable=True, comment='organization contact email'),
    sa.Column('contact_phone', sa.String(length=20), nullable=True, comment='organization contact phone'),
    sa.Column('created_on', sa.DateTime(), nullable=False),
    sa.Column('last_updated', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_organization_id'), 'organization', ['id'], unique=False)
    op.drop_index('cohort_file_hash_key', table_name='cohort')
    op.create_unique_constraint(None, 'cohort', ['file_hash'])
    op.drop_index('permission_name_key', table_name='permission')
    op.create_unique_constraint(None, 'permission', ['name'])
    op.drop_index('role_name_key', table_name='role')
    op.create_unique_constraint(None, 'role', ['name'])
    op.drop_index('role_permission_uc', table_name='role_permission')
    op.create_unique_constraint('role_permission_uc', 'role_permission', ['role_id', 'permission_id'])
    op.add_column('user', sa.Column('organization_id', sa.Integer(), nullable=True))
    op.alter_column('user', 'name',
               existing_type=sa.VARCHAR(length=100),
               comment='user first name',
               existing_comment='user name',
               existing_nullable=False)
    op.create_index(op.f('ix_user_organization_id'), 'user', ['organization_id'], unique=False)
    op.create_foreign_key(None, 'user', 'organization', ['organization_id'], ['id'], ondelete='RESTRICT')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_index(op.f('ix_user_organization_id'), table_name='user')
    op.alter_column('user', 'name',
               existing_type=sa.VARCHAR(length=100),
               comment='user name',
               existing_comment='user first name',
               existing_nullable=False)
    op.drop_column('user', 'organization_id')
    op.drop_constraint('role_permission_uc', 'role_permission', type_='unique')
    op.create_index('role_permission_uc', 'role_permission', ['role_id', 'permission_id'], unique=True)
    op.drop_constraint(None, 'role', type_='unique')
    op.create_index('role_name_key', 'role', ['name'], unique=True)
    op.drop_constraint(None, 'permission', type_='unique')
    op.create_index('permission_name_key', 'permission', ['name'], unique=True)
    op.drop_constraint(None, 'cohort', type_='unique')
    op.create_index('cohort_file_hash_key', 'cohort', ['file_hash'], unique=True)
    op.drop_index(op.f('ix_organization_id'), table_name='organization')
    op.drop_table('organization')
    # ### end Alembic commands ###
