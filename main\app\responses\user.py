from pydantic import BaseModel


class UserResponsesClass(BaseModel):
    LOGIN_SUCCESS: dict = {
        "success": True,
        "message": "logged in successfully.",
    }

    LOGIN_INVALID_CREDENTIALS: dict = {
        "success": False,
        "message": "invalid credentials, recheck password and email.",
    }

    CREATE_SUCCESS: dict = {
        "success": True,
        "message": "accounts created successfully.",
    }

    CREATE_ACCOUNT_EXISTS: dict = {
        "success": False,
        "message": "account already exists.",
    }

    CREATE_TOKEN_INVALID: dict = {
        "success": False,
        "message": "invalid token or token expired.",
    }

    UPDATE_FAILED: dict = {
        "success": False,
        "message": "failed to update.",
    }

    TOKEN_REFRESH_SUCCESS: dict = {
        "success": True,
        "message": "token refreshed successfully.",
    }

    USER_NOT_EXIST: dict = {
        "success": True,
        "message": "No user found.",
    }

    ROLE_NOT_EXIST: dict = {
        "success": True,
        "message": "No role found.",
    }

    USER_DELETED_SUCCESS: dict = {
        "success": True,
        "message": "User deleted successfully.",
    }

    INVITATION_UNSUCCESSFUL: dict = {
        "success": False,
        "message": "failed to send invitation.",
    }

    INVITE_ROLE_INVALID: dict = {
        "success": False,
        "message": "invalid role",
    }

    COHORT_ALREADY_EXISTS: dict = {
        "success": False,
        "message": "Users with this are already created.",
    }

    USER_UPDATED: dict = {
        "success": True,
        "message": "The user updated successfully.",
    }

    PASSWORD_SENT: dict = {
        "success": True,
        "message": "New Password sent to your email.",
    }

    UPDATE_OWN_ACCOUNT: dict = {
        "success": True,
        "message": "You cannot update your own data.",
    }

    DELETE_OWN_ACCOUNT: dict = {
        "success": True,
        "message": "You cannot delete your own data.",
    }

    DELETE_FAILED: dict = {
        "success": False,
        "message": "failed to delete user.",
    }

    INCORRECT_FILE_TYPE: dict = {
        "success": False,
        "message": "Incorrect file type.",
    }

    USER_DETAILS_RETRIEVED: dict = {
        "success": True,
        "message": "User details retrieved successfully.",
    }


UserResponses = UserResponsesClass()
