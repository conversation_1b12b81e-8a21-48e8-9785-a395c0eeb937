"""
Script to set up HeygenVideo functionality
"""

import os
import sys
from sqlalchemy import create_engine, Column, String, MetaData, Table, inspect
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Import the scripts
from scripts.add_heygen_video_scopes import add_heygen_video_scopes
from scripts.add_public_url_to_heygen_video import add_public_url_column

def setup_heygen_video():
    """Run all setup steps for HeygenVideo functionality"""
    print("Setting up HeygenVideo functionality...")
    
    # Step 1: Add the public_url column to the heygen_video table
    print("\nStep 1: Adding public_url column to heygen_video table...")
    add_public_url_column()
    
    # Step 2: Add HeygenVideo scopes to the database
    print("\nStep 2: Adding HeygenVideo scopes to the database...")
    add_heygen_video_scopes()
    
    print("\nHeygenVideo setup completed!")

if __name__ == "__main__":
    setup_heygen_video()
