from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# Revision identifiers, used by Alembic.
revision = '1571bc56ad6d'
down_revision = '1571bc56ad6f'
branch_labels = None
depends_on = None

# Define the old and new Enum types
old_activitytype = postgresql.ENUM(
    'pre_course_survey', 'quiz', 'tutorial', 'hardware_test', 'video_example',
    'plunge_activity', 'practice_activity', 'assessment', 'boss_challenge',
    'recap', 'feedback', name='activitytype'
)

new_activitytype = postgresql.ENUM(
    'pre_course_survey', 'quiz', 'tutorial', 'hardware_test', 'video_example',
    'plunge_activity', 'practice_activity', 'assessment', 'boss_challenge',
    'recap', 'feedback', 'additional_resources', name='activitytype'
)

def upgrade():
    # Add the new type to the ENUM
    op.execute("ALTER TYPE activitytype ADD VALUE 'additional_resources'")


def downgrade():
    # Enum downgrades are tricky; it may require additional logic depending on your DBMS
    pass