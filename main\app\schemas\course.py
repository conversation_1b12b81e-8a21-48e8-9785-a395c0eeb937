from datetime import datetime

from pydantic import BaseModel, EmailStr, constr
from .progress import Progress
from .activity import ActivityCourseReturn, ActivityCreate
from typing import List


class CourseCreate(BaseModel):
    name: str
    activities: List[ActivityCourseReturn] = []
    instructor: int | None = None
    
    class Config:
        orm_mode = True
    
class CourseInDB(CourseCreate):
    created_at: datetime | None
    
class CourseUpdate(BaseModel):
    name: str = ""
    activities: List[int] = []
    
class CourseReturn(BaseModel):
    id: int | None = None
    created_at: datetime | None
    updated_at: datetime | None
    
class CourseDetailReturn(BaseModel):
    id: int | None = None
    name: str | None = None
    activities: List[ActivityCourseReturn] = []
    created_at: datetime | None
    updated_at: datetime | None
    
    class Config:
        orm_mode = True
    
class CourseProgressReturn(BaseModel):
    id: int | None = None
    activities: List = []
    progress: Progress | None
    created_at: datetime | None
    updated_at: datetime | None
    
    class Config:
        orm_mode = True