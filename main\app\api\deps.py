from typing import Generator, List, Optional, Union, Any

from app import crud, models
from app.core.auth import oauth2_scheme
from app.core.config import settings
from app.db.session import SessionLocal
from app.responses.base import APIException, APIResponse
from app.responses import (
    JWTResponses,
)
from app.services import EmailClient

from fastapi import Depends, status
from fastapi.security import SecurityScopes
from jose import JWTError, jwt
from pydantic import BaseModel
from sqlalchemy.orm import Session


current_user_role: models.Role


class Token(BaseModel):
    """Token Pydantic Model"""

    access_token: str
    token_type: str


class TokenData(BaseModel):
    """Token Data Pydantic Model"""

    username: Optional[str] = None
    scopes: List[str] = []
    token_type: str
    forgot_password: bool = False


def get_db() -> Generator:
    """Get a database session.

    Yields:
        Generator: A database session.
    """

    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_current_user_role(db: Session, current_user: models.User):
    global current_user_role
    current_user_role = (
        db.query(models.Role).filter(models.Role.id == current_user.role_id).first()
    )


def get_email_client() -> EmailClient:
    """Get an email client.

    Returns:
        EmailClient: An email client.
    """
    return EmailClient(
        **settings.email.dict(),
    )


def decode_jwt_token(token: str):
    # Decode JWT token and extract username, type, and scopes
    try:
        payload = jwt.decode(
            token, settings.jwt.JWT_SECRET_KEY, algorithms=[settings.jwt.JWT_ALGORITHM]
        )
        # username will always hold the id for current user coming from token
        username: str = payload.get("sub")
        if username is None:
            return None
        token_scopes = payload.get("scopes", [])
        forgot_password = payload.get("forgot_password", False)
        token_data = TokenData(
            scopes=token_scopes,
            username=username,
            token_type=payload.get("type"),
            forgot_password=forgot_password,
        )
    except JWTError:
        return None

    return token_data


async def get_current_user(
    security_scopes: SecurityScopes,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db),
) -> Union[models.User, APIException]:
    """Get the current user. Verify token and scopes.

    Args:
        security_scopes (SecurityScopes): The security scopes or permissions
        token (str): JWT token payload. Defaults to Depends(oauth2_scheme).
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).

    Raises:
        APIException: If the token is invalid or the user is not found.

    Returns:
        Union[User, APIException]: The current user or an APIException.
    """
    user = None
    # Add scopes to authenticate_value if required
    if security_scopes.scopes:
        authenticate_value = f'Bearer scope="{security_scopes.scope_str}"'
    else:
        authenticate_value = f"Bearer"

    credentials_exception = APIException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        message=APIResponse(**JWTResponses.INVALID_CREDENTIALS),
    )

    token_data = decode_jwt_token(token)
    if token_data is None:
        raise credentials_exception

    user = crud.user.get(db, id=token_data.username)

    if user is None:
        raise credentials_exception

    # Check if user belongs to an organization and if that organization is on hold
    from app.utils.globals import AvailableRoles
    from app.models.organization import OrganizationStatus

    user_role = crud.role.get(db=db, id=user.role_id).name

    # Only check organization status for non-super-admin users
    if user_role != AvailableRoles.super_admin and user.organization_id:
        # Get the organization
        organization = db.query(models.Organization).filter(models.Organization.id == user.organization_id).first()
        if organization and organization.status == OrganizationStatus.HOLD:
            raise APIException(
                status_code=status.HTTP_403_FORBIDDEN,
                message=APIResponse(
                    success=False,
                    message="Your organization account is currently on hold. Please contact the administrator.",
                ),
            )

    # initializing the scope_found
    scope_found = False
    for scope in security_scopes.scopes:
        # if we find the scope in the token we simply break the loop and makes the scope_found true
        if scope in token_data.scopes:
            scope_found = True
            break
        # otherwise we will continue with next API scope defined in the endpoint
        continue

    # if scope not found we raise exception
    if not scope_found:
        raise APIException(
            status_code=status.HTTP_403_FORBIDDEN,
            message=APIResponse(**JWTResponses.NOT_ENOUGH_PERMISSIONS),
            headers={"WWW-Authenticate": authenticate_value},
        )
    if type(user) == models.User:
        get_current_user_role(db, current_user=user)
    db.close_all()

    return user


def get_token_data(
    security_scopes: SecurityScopes,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db),
):
    token_data = decode_jwt_token(token)
    if token_data is None:
        return None
    return token_data


# Get user scopes
def get_user_scopes(db: Session, user: Any) -> List[str]:
    """Get user scopes.

    Args:
        db (Session): SQLAlchemy Session.
        user_id (int): User ID.

    Returns:
        List[str]: User scopes.
        :param db:
        :param user:
    """
    permissions = (
            db.query(models.Permission)
            .join(models.RolePermission)
            .filter(models.RolePermission.role_id == user.role_id)
        )
    # Format permissions for OAuth2 Scopes
    scopes = [permission.name for permission in permissions]

    db.close_all()
    return scopes

