from pydantic import BaseModel, EmailStr

class AuthLogin(BaseModel):
    username: EmailStr
    password: str
    
class LoginDataObj(BaseModel):
    access_token: str | None
    token_type: str | None
    refresh_token: str | None

class AuthLoginOut(BaseModel):
    data: LoginDataObj | None | str

class AuthRefresh(BaseModel):
    refresh_token: str


class AuthForgotPassword(BaseModel):
    email: EmailStr

