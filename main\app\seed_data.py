from app import crud
from app.models import CourseActivityAssociation, MCQS
from app.scopes import *
from app.api import deps
from app.logger import logger
from app.utils.globals import create_info_logging_message
from app.schemas import RoleCreate, PermissionCreate, RolePermissionCreate, ActivityCreate, CourseCreate
from app.db.session import <PERSON>L<PERSON>al

from fastapi import Depends

from main.app.scopes.heygen import HeyGenScopes
from main.app.scopes.heygen_video import HeygenVideoScopes
from sqlalchemy.orm import Session


class Role:
    role = ""
    permissions = []

    def __init__(self, db: Session):
        self.db = db
        self.create_role_and_assign_permissions()

    def create_role(self):
        db_role = crud.role.get_by_name(db=self.db, name=self.role)
        if not db_role:
            role_in = RoleCreate(name=self.role)
            role_out = crud.role.create(db=self.db, obj_in=role_in)
            logger.info(
                create_info_logging_message(
                        endpoint="/create",
                        feature="role",
                        message=f"Role {self.role} created succesfully",
                    )
                )
            role_id = role_out.id
        else:
            logger.info(
                create_info_logging_message(
                        endpoint="/create",
                        feature="role",
                        message=f"Role {self.role} already exists",
                    )
                )
            role_id = db_role.id
        return role_id

    def create_permission(self, permission: str):
        db_permission = crud.permission.get_by_name(db=self.db, name=permission)
        if not db_permission:
            permission_in = PermissionCreate(name=permission)
            permission_out = crud.permission.create(db=self.db, obj_in=permission_in)
            logger.info(
            create_info_logging_message(
                    endpoint="/create",
                    feature="permission",
                    message=f"Permission {permission} created succesfully",
                )
            )
            permission_id = permission_out.id
        else:
            logger.info(
            create_info_logging_message(
                    endpoint="/create",
                    feature="permission",
                    message=f"Permission {permission} already exists",
                )
            )
            permission_id = db_permission.id
        return permission_id

    def create_role_permission(self, role_id: int, permission_id: int):
        if not crud.role_permission.get_role_permission_by_id(db=self.db, role_id=role_id, permission_id=permission_id):
            role_permission_in = RolePermissionCreate(role_id=role_id, permission_id=permission_id)
            crud.role_permission.create(db=self.db, obj_in=role_permission_in)
            logger.info(
            create_info_logging_message(
                    endpoint="/create",
                    feature="permission",
                    message=f"Permission {permission_id} succesfully assigned to role {role_id}",
                )
            )
        else:
            logger.info(
            create_info_logging_message(
                    endpoint="/create",
                    feature="permission",
                    message=f"Permission {permission_id} is already assigned to role {role_id}",
                )
            )

    def create_role_and_assign_permissions(self):
        role_id = self.create_role()
        for permission in self.permissions:
            permission_id = self.create_permission(permission=permission)
            self.create_role_permission(role_id=role_id, permission_id=permission_id)


class SuperAdmin(Role):
    role = "super_admin"
    permissions = [RoleScopes.create, RoleScopes.update, RoleScopes.read, RoleScopes.delete,
                   PermissionScopes.create, PermissionScopes.update, PermissionScopes.read, PermissionScopes.delete,
                   RolePermissionScopes.create, RolePermissionScopes.update, RolePermissionScopes.read, RolePermissionScopes.delete,
                   UserScopes.invite, UserScopes.update, UserScopes.read, UserScopes.delete,
                   OrganizationScopes.create, OrganizationScopes.read, OrganizationScopes.update,
                   OrganizationScopes.delete, OrganizationScopes.update_status, OrganizationScopes.create_admin,
                   CourseScopes.create, CourseScopes.update, CourseScopes.read, CourseScopes.delete,
                   ConversationScopes.create, ConversationScopes.update, ConversationScopes.read, ConversationScopes.delete,
                   ActivityScopes.create, ActivityScopes.update, ActivityScopes.read, ActivityScopes.delete]


class Admin(Role):
    role = "admin"
    permissions = [UserScopes.invite, UserScopes.update, UserScopes.read, UserScopes.delete,
                   OrganizationScopes.read,
                   ConversationScopes.create, ConversationScopes.update, ConversationScopes.read, ConversationScopes.delete,
                   CourseScopes.create, CourseScopes.update, CourseScopes.read, CourseScopes.delete,
                   ActivityScopes.create, ActivityScopes.update, ActivityScopes.read, ActivityScopes.delete]


class Instructor(Role):
    role = "instructor"
    permissions = [CourseScopes.create, CourseScopes.update, CourseScopes.read, CourseScopes.delete,
                   ActivityScopes.create, ActivityScopes.update, ActivityScopes.read, ActivityScopes.delete,
                   ConversationScopes.create, ConversationScopes.update, ConversationScopes.read, ConversationScopes.delete,
                   CourseProgressScopes.create, CourseProgressScopes.update, CourseProgressScopes.read, CourseProgressScopes.delete,
                   CohortScopes.read,
                   UserScopes.update, UserScopes.read, HeyGenScopes.create_token,
                   HeygenVideoScopes.create, HeygenVideoScopes.read, HeygenVideoScopes.update,
                   HeygenVideoScopes.delete, HeygenVideoScopes.use_in_activity]

class Learner(Role):
    role = "learner"
    permissions = [UserScopes.update, UserScopes.read,
                   CourseScopes.read,
                   AuthScopes.update_password,
                   ActivityScopes.read,
                   CourseProgressScopes.create, CourseProgressScopes.read,
                   ActivityProgressScopes.create, ActivityProgressScopes.update, ActivityProgressScopes.read,
                   ConversationScopes.create, ConversationScopes.update, ConversationScopes.read]


class ActivityIn:
    name = ""
    type = ""
    questions = []
    mcqs = []
    retries_allowed = None

    def __init__(self, db: Session):
        self.db = db

    def create_activity(self):
        activity_in = ActivityCreate(
                name=self.name,
                type=self.type,
                questions=self.questions,
                mcqs=[],
                retries_allowed=self.retries_allowed,
            )
        if self.type == "pre_course_survey":
            activity_in.questions = self.questions
        activity_out = crud.activity.create(db=self.db, obj_in=activity_in)
        logger.info(
            create_info_logging_message(
                    endpoint="/create",
                    feature="activity",
                    message=f"Activity {self.name} of type {self.type} created succesfully",
                )
            )
        if self.type == "quiz":
            mcqs_list = [MCQS(activity_id=activity_out.id, **each) for each in self.mcqs]
            self.db.add_all(mcqs_list)
            self.db.commit()
            logger.info(
            create_info_logging_message(
                    endpoint="/create",
                    feature="activity",
                    message=f"{len(mcqs_list)} assigned to activity {activity_out.id}",
                )
            )
        return activity_out

    def get_activity(self):
        return crud.activity.get_by_type(db=self.db, type=self.type)


class PRE_COURSE_SURVEY(ActivityIn):
    name = "pre_course_survey"
    type = "pre_course_survey"
    questions = ["Why did you decide to enroll in this course?", "What are your main goals or objectives for this course?",
                 "Please rate your current knowledge in the course subject on a scale of 1 to 5 (1 being no knwledge and 5 being expert-level knowledge)", "Do you have any specific topics or skills within this course that you are particularly interested in?",
                 "Are there any particular challenges or concerns you hope to address by taking this course?",
                 "Why did you decide to enroll in this course?",
                 "What are your main goals or objectives for this course?"]


class TUTORIAL(ActivityIn):
    name = "tutorial"
    type = "tutorial"

class PLUNGE_ACTIVTY(ActivityIn):
    name = "plunge_activity"
    type = "plunge_activity"

class HARDWARE_TEST(ActivityIn):
    name = "hardware_test"
    type = "hardware_test"

class PRACTICE_ACTIVTY(ActivityIn):
    name = "practice_activity"
    type = "practice_activity"
    retries_allowed = 2

class VIDEO_EXAMPLE(ActivityIn):
    name = "video_example"
    type = "video_example"

class RECAP(ActivityIn):
    name = "recap"
    type = "recap"

class FEEDBACK(ActivityIn):
    name = "feedback"
    type = "feedback"
    questions = ["Why did you decide to enroll in this course?", "What are your main goals or objectives for this course?",
                 "Please rate your current knowledge in the course subject on a scale of 1 to 5 (1 being no knwledge and 5 being expert-level knowledge)", "Do you have any specific topics or skills within this course that you are particularly interested in?",
                 "Are there any particular challenges or concerns you hope to address by taking this course?",
                 "Why did you decide to enroll in this course?",
                 "What are your main goals or objectives for this course?"]

class BOSS_CHALLENGE(ActivityIn):
    name = "boss_challenge"
    type = "boss_challenge"
    retries_allowed = 1

class QUIZ(ActivityIn):
    name = "quiz"
    type = "quiz"
    retries_allowed = 1
    mcqs = [
        {
            "question": "What is capital of France?",
            "options": ["Islamabad", "Paris", "Ottawa", "Sydney"],
            "right_option": "Paris"
        },
        {
            "question": "What is the capital of Japan?",
            "options": ["Tokyo", "Beijing", "Seoul", "Bangkok"],
            "right_option": "Tokyo"
        },
        {
            "question": "What is the capital of Canada?",
            "options": ["Toronto", "Vancouver", "Ottawa", "Montreal"],
            "right_option": "Ottawa"
        },
        {
            "question": "What is the capital of Australia?",
            "options": ["Melbourne", "Sydney", "Canberra", "Brisbane"],
            "right_option": "Canberra"
        },
        {
            "question": "What is the capital of Germany?",
            "options": ["Berlin", "Munich", "Frankfurt", "Hamburg"],
            "right_option": "Berlin"
        },
        {
            "question": "What is the capital of Italy?",
            "options": ["Venice", "Milan", "Rome", "Florence"],
            "right_option": "Rome"
        }
    ]


class CourseIn:
    name = "AI Dignity"
    instructor = None

    def __init__(self, db: Session, activities=[]):
        self.db = db
        course_in = CourseCreate(
            name=self.name,
        )
        course_out = crud.course.create(db=self.db, obj_in=course_in)
        db.add_all([CourseActivityAssociation(course_id=course_out.id, activity_id=getattr(a, "id"), sequence=idx)
                    for idx, a in enumerate(activities)])
        db.commit()
        logger.info(
            create_info_logging_message(
                    endpoint="/create",
                    feature="course",
                    message=f"Course {self.name} created succesfully",
                )
            )



db = SessionLocal()

# super_admin = SuperAdmin(db=db)
# admin = Admin(db=db)
instructor = Instructor(db=db)
# learner = Learner(db=db)

# hardware_test = HARDWARE_TEST(db=db).create_activity()
# pre_course_survey = PRE_COURSE_SURVEY(db=db).create_activity()
# plunge_activity = PLUNGE_ACTIVTY(db=db).create_activity()
# tutorial_1 = TUTORIAL(db=db).create_activity()
# quiz = QUIZ(db=db).create_activity()
# practice_activity = PRACTICE_ACTIVTY(db=db).create_activity()
# tutorial_2 = TUTORIAL(db=db).create_activity()
# tutorial_3 = TUTORIAL(db=db).create_activity()
# recap = RECAP(db=db).create_activity()
# boss_challenge = BOSS_CHALLENGE(db=db).create_activity()
# feedback = FEEDBACK(db=db).create_activity()


# pre_course_survey = PRE_COURSE_SURVEY(db=db).get_activity()
# tutorial = TUTORIAL(db=db).get_activity()
# ai_activity = AI_ACTIVTY(db=db).get_activity()
# video_example = VIDEO_EXAMPLE(db=db).get_activity()
# quiz = QUIZ(db=db).get_activity()
# recap = RECAP(db=db).get_activity()
# feedback = FEEDBACK(db=db).get_activity()

# CourseIn(db=db, activities=[hardware_test, pre_course_survey, plunge_activity, tutorial_1, quiz, practice_activity, tutorial_2,
#                             tutorial_3, recap, boss_challenge, feedback])

db.close()
