"""
This file contains all the API's related to 
roles & permissions
"""

from typing import Any
from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.responses import RolePermissionResponses, GeneralResponses
from app.scopes import RolePermissionScopes
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status
from fastapi.encoders import jsonable_encoder
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

router = APIRouter()


@router.post("/create", response_model=schemas.RolePermission | Any)
def create_role_permission(
    *,
    role_permission_in: schemas.RolePermissionCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RolePermissionScopes.create]
    ),
) -> JSONResponse:
    """Create new Role & Permission

    Args:
        role_permission_in (schemas.RolePermissionCreate): RolePermissionCreate Pydantic Model role & permission to create
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user, scopes=[RolePermissionScopes.create]).

    Returns:
        JSONResponse: JSONResponse containing current default Role & Permission
    """

    try:

        if not crud.role_permission.get_role_permission_by_id(
            db=db,
            role_id=role_permission_in.role_id,
            permission_id=role_permission_in.permission_id,
        ):
            # Get payload & create role & permission
            role_permission = crud.role_permission.create(
                db=db, obj_in=role_permission_in
            )
            # Return a Pydantic Validated role & permission Model
            role_permission_dict = role_permission.to_dict()

            return role_permission_dict

        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="role permission",
                message=str(RolePermissionResponses.ROLE_PERMISSION_EXISTS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content=APIResponse(
                **RolePermissionResponses.ROLE_PERMISSION_EXISTS,
            ),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.delete("/delete", response_model=schemas.RolePermission)
def delete_role_permission(
    *,
    role_permission_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RolePermissionScopes.delete]
    ),
) -> JSONResponse:
    """Delete exiting role & permission 

    Args:
        payload_delete_default_role_permission (schemas.DefaultRolePermissionDelete): DefaultRolePermissionDelete Pydantic Model Default Role & Permission to delete
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing response message
    """

    try:
        # Get by role_permission_id
        role_permission = crud.role_permission.get(db=db, id=role_permission_id)

        if not role_permission:
            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="role permission",
                    message=str(RolePermissionResponses.ROLE_PERMISSION_NOT_FOUND["message"]),
                )
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=APIResponse(
                    **RolePermissionResponses.ROLE_PERMISSION_NOT_FOUND,
                ),
            )

        # Delete role & permission
        role_permission = crud.role_permission.remove(db=db, id=role_permission.id)

        # Return a Pydantic Validated default roles & permissions Model
        role_permission_dict = role_permission.to_dict()
        role_permission_dict = schemas.RolePermission(**role_permission_dict)
        role_permission_out = jsonable_encoder(role_permission_dict)

        logger.info(
            create_info_logging_message(
                endpoint="/delete",
                feature="role permission",
                message=str(RolePermissionResponses.ROLE_PERMISSION_DELETE_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(
                **RolePermissionResponses.ROLE_PERMISSION_DELETE_SUCCESS,
                data=role_permission_out,
            ),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/get/all", response_model=Page[schemas.RolePermissionListOut])
def all_role_and_permissions(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RolePermissionScopes.read]
    ),
) -> JSONResponse:
    """Fetch all role & permissions

    Args:
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing all default role & permissions
    """

    try:
        return paginate(
            db.query(
                models.RolePermission.id,
                models.Role.name.label("role"),
                models.Permission.name.label("permission"),
                models.RolePermission.created_on,
                models.RolePermission.last_updated,
            )
            .join(
                models.RolePermission,
                models.Role.id == models.RolePermission.role_id,
            )
            .join(
                models.Permission,
                models.Permission.id == models.RolePermission.permission_id,
            )
        )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/get/{role_permission_id}", response_model=schemas.RolePermission)
def get_role_permission(
    *,
    role_permission_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RolePermissionScopes.read]
    ),
) -> JSONResponse:
    """Get role & permission by id

    Args:
        role_permission_id (int): Role & Permission Id
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing response message
    """

    try:
        # Check if role_permission exists or not
        get_role_permission = crud.role_permission.get(db=db, id=role_permission_id)
        if get_role_permission:
            # Return a Pydantic Validated default roles & permissions Model
            role_permission_dict = get_role_permission.to_dict()
            role_permission_dict = schemas.RolePermission(**role_permission_dict)
            role_permission_out = jsonable_encoder(role_permission_dict)
            
            logger.info(
                create_info_logging_message(
                    endpoint="/get",
                    feature="role permission",
                    message=str(RolePermissionResponses.ROLE_PERMISSION_GET_SUCCESS["message"]),
                )
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=APIResponse(
                    **RolePermissionResponses.ROLE_PERMISSION_GET_SUCCESS,
                    data=role_permission_out,
                ),
            )
        logger.info(
            create_info_logging_message(
                endpoint="/get",
                feature="role permission",
                message=str(GeneralResponses.DATA_DOES_NOT_EXITS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(**GeneralResponses.DATA_DOES_NOT_EXITS),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

