import re
import os

import base64
import numpy as np

from enum import IntEnum
from strenum import StrEnum

from app.services.email import EmailClient
from app.core.config import settings


# regex for email
email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
invitation_link = "https://IAL.com"



email_client = EmailClient(
    settings.email.EMAIL_FROM_EMAIL,
    settings.email.EMAIL_FROM_NAME,
    settings.email.EMAIL_SMTP_TLS,
    settings.email.EMAIL_SMTP_SSL,
    settings.email.EMAIL_SMTP_PORT,
    settings.email.EMAIL_SMTP_HOST,
    settings.email.EMAIL_SMTP_USERNAME,
    settings.email.EMAIL_SMTP_PASSWORD,
)



class AvailableRoles(StrEnum):
    super_admin = "super_admin"
    admin = "admin"
    learner = "learner"
    instructor = "instructor"

class OrderBy(StrEnum):
    desc = "desc"
    asc = "asc"


def create_info_logging_message(endpoint: str, feature: str, message=None):
    '''
    Function to create info related logging
    '''

    info_dict = {
        "Feature": feature,
        "Endpoint": endpoint,
        "Message": message or "Successfully return the response."
    }

    return info_dict
