"""
This file contains all the crud operations for 
pre course survey related PreCourses
"""

from typing import Optional
from datetime import datetime

from app.crud.base import CRUDBase
from app.models.pre_course import PreCourse

from app.schemas import PreCourseCreate, PreCourseUpdate

from sqlalchemy.orm import Session


class CRUDPreCourse(CRUDBase[PreCourse, PreCourseCreate, PreCourseUpdate]):
    def get_by_question_text(self, db: Session, *, question: str) -> Optional[PreCourse]:
        return db.query(PreCourse).filter(PreCourse.question == question).first()
    
    def get_by_id(self, db: Session, *, id: int) -> Optional[PreCourse]:
        return db.query(PreCourse).filter(PreCourse.id == id).first()
    
    def get_by_course_id(self, db: Session, *, course_id: int) -> Optional[PreCourse]:
        return db.query(PreCourse).filter(PreCourse.course_id == course_id).all()
    
pre_course = CRUDPreCourse(PreCourse)