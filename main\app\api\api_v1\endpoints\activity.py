"""
This file contains all the API's related to activity
"""

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.services import EmailClient
from app.utils.file_handler import upload_file_to_s3, generate_presigned_url
from app.scopes import ActivityScopes, UserScopes, AuthScopes
from app.responses import ActivityResponses, GeneralResponses, AuthResponses, RoleResponses
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status, BackgroundTasks, File, UploadFile, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
import json
from typing import List

router = APIRouter()


def update_mcqs(db: Session, activity_id: int, mcqs_list: list):
    db.query(models.MCQS).filter(
        models.MCQS.activity_id == activity_id).delete()
    db.add_all(mcqs_list)
    db.commit()


def delete_activity_with_progress(db: Session, activity: models.Activity):
    if activity.type == models.ActivityType.quiz:
        # Delete all mcqs of activity
        db.query(models.MCQS).filter(
            models.MCQS.activity_id == activity.id).delete()
    # Delete activity progress
    db.query(models.ActivityProgress).filter(
        models.ActivityProgress.activity_id == activity.id).delete()
    db.commit()
    crud.activity.remove(db=db, id=activity.id)


def update_questions(db: Session, activity_id: int, questions: list):
    db.query(models.Activity).filter(models.Activity.id ==
                                     activity_id).update({"questions": questions})
    db.commit()


@router.post(
    "/create",
    status_code=status.HTTP_200_OK,
    response_model=schemas.ActivityReturn,
)
def create_activity(
    *,
    # background_tasks: BackgroundTasks,
    activity_in: schemas.ActivityCreateBase,
    db: Session = Depends(deps.get_db),
    # video_file: UploadFile = File(default=None),
    # email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ActivityScopes.create]
    ),
) -> JSONResponse:
    """Create a new activity

    Args:\n
        request_data


    """

    try:
        # Create activity in Activity table
        activity_out = crud.activity.create(db=db, obj_in=activity_in)

        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="activity",
                message=str(ActivityResponses.CREATE_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **ActivityResponses.CREATE_SUCCESS,
                data={
                    "id": activity_out.id,
                    "name": activity_out.name,
                    "type": str(activity_out.type),
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**ActivityResponses.CREATE_FAILED),
        )


@router.get(
    "/get/{activity_id:int}",
    status_code=status.HTTP_200_OK,
)
def get_activity_by_id(
    activity_id: int = None,
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ActivityScopes.read]
    ),
) -> JSONResponse:
    """Get activity by activity id

    Args:\n
        request_data


    """
    try:
        activity = crud.activity.get(db=db, id=activity_id)
        if activity.type == models.ActivityType.quiz and activity.mcqs:
            activity.mcqs = activity.mcqs
        if activity.video:
            activity.video = generate_presigned_url(activity.video)
        if activity.extra_fields:
            for field_key in ["instructions", "instructions_below", "instructions_cp", "instructions_cp_below", 
                              "instructions_rec", "instructions_rec_below", "instructions_wr", "instructions_wr_below",
                              "mandatory", "show_correct_option", "file_link", "practiceActivityData"]:
                if activity.extra_fields.get(field_key):
                    setattr(activity, field_key, activity.extra_fields[field_key])
            if activity.extra_fields.get("file_path"):
                activity.file_path = generate_presigned_url(
                    activity.extra_fields["file_path"])
            if activity.extra_fields.get("video"):
                activity.second_video = generate_presigned_url(
                    activity.extra_fields["video"])
            if activity.extra_fields.get("title_videos"):
                activity.title_videos = [{"title": e["title"],
                                          "video": generate_presigned_url(e["video"])} for e in activity.extra_fields["title_videos"]]
        user_role = crud.role.get(db=db, id=current_user.role_id).name
        if user_role == "instructor":
            return schemas.ActivityDetailReturnInstructor(**activity.__dict__)
        else:
            return schemas.ActivityDetailReturn(**activity.__dict__)
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.patch(
    "/update/{activity_id}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.ActivityReturn,
)
def activity_update(
    *,
    activity_id: int,
    name: str = Form(default=""),
    type: str = Form(default=""),
    mcqs: str = Form(default=""),
    questions: str = Form(default=""),
    instructions: str = Form(default=""),
    instructions_below: str = Form(default=""),
    instructions_cp: str = Form(default=""),
    instructions_cp_below: str = Form(default=""),
    instructions_rec: str = Form(default=""),
    instructions_rec_below: str = Form(default=""),
    instructions_wr: str = Form(default=""),
    instructions_wr_below: str = Form(default=""),
    retries_allowed: int = Form(default=None),
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    video_file: UploadFile = File(default=None),
    video_title: str = Form(default=""),
    second_video: UploadFile = File(default=None),
    mandatory: bool = Form(default=False),
    show_correct_option: bool = Form(default=False),
    add_resources_file_link: str = Form(default=""),
    add_resources_file: UploadFile = File(default=None),
    practiceActivityData: str = Form(default=""),
    # email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ActivityScopes.update]
    ),
) -> JSONResponse:
    """Update user data

    Args:\n
        request_data


    """
    try:
        activity = crud.activity.get(db=db, id=activity_id)
        # Verify if user exists in system
        if not activity:
            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="activity",
                    message=str(
                        ActivityResponses.ACTIVITY_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(**ActivityResponses.ACTIVITY_NOT_EXIST),
            )

        if activity.type == models.ActivityType.quiz:
            if mcqs:
                mcqs = json.loads(mcqs)
                mcqs_list = [models.MCQS(
                    activity_id=activity_id, **each) for each in mcqs]
                background_tasks.add_task(
                    update_mcqs(db=db, activity_id=activity.id,
                                mcqs_list=mcqs_list)
                )
        elif activity.type in [models.ActivityType.pre_course_survey, models.ActivityType.feedback]:
            if questions:
                questions = json.loads(questions)
                background_tasks.add_task(
                    update_questions(
                        db=db, activity_id=activity.id, questions=questions)
                )

        folder_path = f"{models.ActivityType(activity.type).value}_{activity.id}"
        activity_update_in = schemas.ActivityUpdate()
        if name:
            activity_update_in.name = name
        if type:
            activity_update_in.type = type
        if retries_allowed is not None:
            activity_update_in.retries_allowed = retries_allowed
        if any([instructions, instructions_below, instructions_cp, instructions_cp_below, instructions_rec, instructions_rec_below,
            instructions_wr, instructions_wr_below]) or \
                mandatory in [True, False] or add_resources_file or add_resources_file_link or show_correct_option in [True, False]:
            activity_update_in.extra_fields = activity.extra_fields if activity.extra_fields else {}
            for field_key in ["instructions", "instructions_below", "instructions_cp", "instructions_cp_below", 
                              "instructions_rec", "instructions_rec_below", "instructions_wr", "instructions_wr_below"]:
                field_val = eval(field_key)
                if field_val:
                    activity_update_in.extra_fields[field_key] = field_val
            if mandatory in [True, False]:
                activity_update_in.extra_fields["mandatory"] = mandatory
            if show_correct_option in [True, False]:
                activity_update_in.extra_fields["show_correct_option"] = show_correct_option
            if add_resources_file_link:
                activity_update_in.extra_fields["file_link"] = add_resources_file_link
            if add_resources_file:
                print(f"Uploading file to s3...")
                s3_path = upload_file_to_s3(
                    folder=folder_path, file=add_resources_file)
                activity_update_in.extra_fields["file_path"] = s3_path
            if practiceActivityData:
                activity_update_in.extra_fields["practiceActivityData"] = json.loads(practiceActivityData)
            db.query(models.Activity).filter(models.Activity.id == activity.id).update({"extra_fields":
                                                                                        activity_update_in.extra_fields})
        # if video file, upload file to s3
        for idx, video in enumerate([video_file, second_video]):
            if video:
                print(f"Uploading video {idx + 1} to s3...")
                s3_path = upload_file_to_s3(folder=folder_path, file=video)

                # Update User in the DB
                if idx == 0 and not video_title:
                    # activity_update_in.video = s3_path
                    db.query(models.Activity).filter(
                        models.Activity.id == activity.id).update({"video": s3_path})
                else:
                    activity_update_in.extra_fields = activity.extra_fields if activity.extra_fields else {}
                    if video_title:
                        videos = activity.extra_fields.get(
                            "title_videos", []) if activity.extra_fields else []
                        # Filter video if there is already one with same title
                        videos = [each for each in videos if each.get(
                            "title") != video_title]
                        videos.append({"title": video_title, "video": s3_path})
                        activity_update_in.extra_fields["title_videos"] = videos
                    else:
                        activity_update_in.extra_fields["video"] = s3_path
                    db.query(models.Activity).filter(models.Activity.id == activity.id).update({"extra_fields":
                                                                                                activity_update_in.extra_fields})

            db.commit()
            # crud.activity.update(db=db, db_obj=activity, obj_in=activity_update_in)
            background_tasks.add_task(
                crud.activity.update(db=db, db_obj=activity,
                                     obj_in=activity_update_in)
            )

        logger.info(
            create_info_logging_message(
                endpoint="/update",
                feature="activity",
                message=str(ActivityResponses.ACTIVITY_UPDATED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **ActivityResponses.ACTIVITY_UPDATED,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.delete(
    "/delete/{activity_id}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.ActivityReturn,
)
def activity_delete(
    *,
    activity_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ActivityScopes.delete]
    ),
) -> JSONResponse:
    """Delete activity data

    Args:\n
        request_data


    """
    try:
        activity = crud.activity.get(db=db, id=activity_id)
        # Verify if user exists in system
        if not activity:
            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="activity",
                    message=str(
                        ActivityResponses.ACTIVITY_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(**ActivityResponses.ACTIVITY_NOT_EXIST),
            )

        # If activity is associated with more than once then do not delete activity from db
        db_activities = db.query(models.CourseActivityAssociation).filter(models.CourseActivityAssociation.activity_id ==
                                                                          activity_id).count()
        if db_activities == 1:
            # Delete activty from DB
            background_tasks.add_task(
                delete_activity_with_progress(db=db, activity=activity))

        logger.info(
            create_info_logging_message(
                endpoint="/delete",
                feature="activity",
                message=str(
                    ActivityResponses.ACTIVITY_DELETED_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **ActivityResponses.ACTIVITY_DELETED_SUCCESS,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/get/all",
    status_code=status.HTTP_200_OK,
    response_model=List[schemas.ActivityReturn]
)
def get_all_activities(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ActivityScopes.read]
    ),
) -> JSONResponse:
    """Get all activities

    Args:\n
        request_data


    """
    try:
        return crud.activity.get_all_activities(db=db)
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
