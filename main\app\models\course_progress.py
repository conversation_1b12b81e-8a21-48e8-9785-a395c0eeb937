"""
This file contains the model mapping of Activity
"""

from datetime import datetime, timezone

from app.db.base_class import Base

from sqlalchemy import (
    Column,
    DateTime,
    String,
    Integer,
    ForeignKey,
    Float
)
from sqlalchemy.orm import relationship
    

class CourseProgress(Base):
    user_id = Column(
        Integer, ForeignKey("user.id", ondelete="RESTRICT"), nullable=False, index=True
    )
    course_id = Column(
        Integer, ForeignKey("course.id", ondelete="RESTRICT"), nullable=False, index=True
    )
    last_activity_progress = Column(
        Integer, ForeignKey("activity_progress.id", ondelete="RESTRICT"), default=None
    )
    progress_percentage = Column(Float, default=0.0)
    user = relationship("User", back_populates="courses_progress")
    course = relationship("Course", lazy="joined")
    # courses = relationship('Course', secondary=course_activity_association, back_populates='activities')
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                        onupdate=lambda: datetime.now(timezone.utc), nullable=False)