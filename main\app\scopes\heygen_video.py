"""
This file contains all the Scopes 
of HeygenVideo related API's/resources
"""

from pydantic import BaseModel


class HeygenVideoScopesClass(BaseModel):
    create: str = "heygen_video:create"
    read: str = "heygen_video:read"
    update: str = "heygen_video:update"
    delete: str = "heygen_video:delete"
    use_in_activity: str = "heygen_video:use_in_activity"


HeygenVideoScopes = HeygenVideoScopesClass()
