"""
This file contains all the crud operations for 
role permission related feature
"""

from typing import Optional

from app.crud.base import CRUDBase
from app.models.role_permission import RolePermission
from app.schemas.role_permission import RolePermissionCreate, RolePermissionUpdate

from sqlalchemy.orm import Session


class CRUDRolePermission(
    CRUDBase[RolePermission, RolePermissionCreate, RolePermissionUpdate]
):
    def get_role_permission_by_id(
        self, db: Session, *, role_id: int, permission_id: int
    ) -> Optional[RolePermission]:
        return (
            db.query(RolePermission)
            .filter(
                RolePermission.permission_id == permission_id,
                RolePermission.role_id == role_id,
            )
            .first()
        )


role_permission = CRUDRolePermission(RolePermission)
