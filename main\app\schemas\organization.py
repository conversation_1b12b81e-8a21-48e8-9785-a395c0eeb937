"""
This file contains all the Schemas 
of Organization feature
"""

from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional, List
from app.models.organization import OrganizationStatus


class OrganizationBase(BaseModel):
    name: str
    description: Optional[str] = ""
    address: Optional[str] = ""
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = ""


class OrganizationCreate(OrganizationBase):
    """Schema for creating a new organization"""
    pass


class OrganizationUpdate(BaseModel):
    """Schema for updating an organization"""
    name: Optional[str] = None
    description: Optional[str] = None
    address: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None


class OrganizationStatusUpdate(BaseModel):
    """Schema for updating organization status"""
    status: OrganizationStatus


class OrganizationInDBBase(OrganizationBase):
    id: Optional[int] = None
    status: OrganizationStatus
    created_on: datetime
    last_updated: datetime

    class Config:
        orm_mode = True


class Organization(OrganizationInDBBase):
    """Schema for returning organization data"""
    pass


class OrganizationWithUsers(Organization):
    """Schema for returning organization with users"""
    users: List = []
