from datetime import datetime

from pydantic import BaseModel
from typing import List, Optional, Union
from app.models import ActivityType


class MCQS(BaseModel):
    id: int
    question: str
    options: List[str] = []
      
    class Config:
        orm_mode = True
        
class MCQCreate(BaseModel):
    id: int | None = None
    question: str
    options: List[str] = []
    right_option: str
      
    class Config:
        orm_mode = True
    
class ActivityCreate(BaseModel):
    name: str
    type: str
    video: str | None = None
    retries_allowed: int | None
    questions: List[str] | None = None
    mcqs: List[MCQCreate] | None = None
    
    class Config:
        orm_mode = True
        
class ActivityCreateBase(BaseModel):
    name: str
    type: str
    questions: List[str] | None = []
    
class ActivityInDB(ActivityCreate):
    created_at: datetime | None
    
class ActivityUpdate(BaseModel):
    name: str | None = None
    type: str | Optional[ActivityType] | None = None
    video: str | None = None
    retries_allowed: int | None
    extra_fields: dict | None
    # questions: List[str] | None = None
    # mcqs: List[MCQCreate] | None = None
    
class ActivityReturn(BaseModel):
    id: int | None = None
    name: str | None = None
    type: str | Optional[ActivityType] | None = None
    
    class Config:
        orm_mode = True
    
class ActivityCourseReturn(BaseModel):
    id: int | None = None
    name: str | None
    type: str | Optional[ActivityType]
    sequence: int | None
    completed: bool | None = None
    
    class Config:
        orm_mode = True
        

class Video(BaseModel):
    title: str
    video: str
    
class FeedbackQuesions(BaseModel):
    question: str
    options: List[str] = []
        
class ActivityDetailReturn(BaseModel):
    id: int | None = None
    name: str | None
    instructions: str | None = ""
    instructions_below: str | None = ""
    instructions_cp: str | None = ""
    instructions_cp_below: str | None = ""
    instructions_rec: str | None = ""
    instructions_rec_below: str | None = ""
    instructions_wr: str | None = ""
    instructions_wr_below: str | None = ""
    type: Optional[ActivityType]
    questions: List[str] | List[FeedbackQuesions] = []
    video: str | None
    second_video: str | None = None
    mandatory: bool = False
    show_correct_option: bool = False
    file_link: str | None = ""
    file_path: str | None = ""
    file: str | None = ""
    mcqs: List[MCQS] = []
    title_videos: List[Video] = []
    practiceActivityData: dict | None = None
    
    class Config:
        orm_mode = True
        
class ActivityDetailReturnInstructor(ActivityDetailReturn):
    mcqs: List[MCQCreate] = []
    
    class Config:
        orm_mode = True
    
        