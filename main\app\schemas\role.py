"""
This file contains all the Schemas 
of Role feature
"""

from pydantic import BaseModel
from datetime import datetime


class RoleBase(BaseModel):
    name: str


class RoleCreate(RoleBase):
    ...


class RoleUpdate(RoleBase):
    name: str | None


class RoleDelete(BaseModel):
    ...


class RoleInDBBase(RoleBase):
    id: int | None = None
    created_on: datetime
    last_updated: datetime

    class Config:
        orm_mode = True


class Role(RoleInDBBase):
    ...
