from datetime import datetime

from pydantic import BaseModel, EmailStr, constr
from .course import CourseCreate
from typing import List


class CourseProgressCreate(BaseModel):
    course_id: int
    user_id: int | None = None
    
    class Config:
        orm_mode = True
        
class CourseProgressRequest(BaseModel):
    course_id: int
        
class CourseProgressCohortRequest(BaseModel):
    course_id: int
    cohort_id: int
    

class CourseProgressRequestMultiple(BaseModel):
    course_id: int
    user_id: int
    
class CourseProgressInDB(CourseProgressCreate):
    
    created_at: datetime | None
    
class CourseProgressUpdate(BaseModel):
    progress_percentage: float | None = None
    
    class Config:
        orm_mode = True
    
class CourseProgressReturn(BaseModel):
    course_id: int | None = None
    user_id: int | None = None
    course: CourseCreate
    progress_percentage: float | None = None
    created_at: datetime | None
    updated_at: datetime | None
    
    class Config:
        orm_mode = True
    
# class CourseProgressProgressReturn(BaseModel):
#     id: int | None = None
#     activities: List = []
#     progress: Progress | None
#     created_at: datetime | None
#     updated_at: datetime | None
    
#     class Config:
#         orm_mode = True