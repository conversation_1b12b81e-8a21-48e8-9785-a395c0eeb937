"""
This file contains all the API's related to conversation
"""

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.services import EmailClient
from app.core.ai_conversation import get_response_from_chatbot
from app.scopes import ConversationScopes
from app.responses import ConversationResponses, GeneralResponses, AuthResponses, RoleResponses
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.orm import Session
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import parse_obj_as
from typing import List
import json

router = APIRouter()


@router.post(
    "/message",
    status_code=status.HTTP_200_OK,
    response_model=schemas.ConversationReturn,
)
def send_message_to_chatbot(
    *,
    # background_tasks: BackgroundTasks,
    request_data: schemas.ConversationCreate,
    db: Session = Depends(deps.get_db),
    # email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ConversationScopes.create]
    ),
) -> JSONResponse:
    """send message to chatbot

    Args:\n
        request_data


    """
    conversation_out = None
    if request_data.conversation_id is not None:
        conversation_out = crud.conversation.get(db=db, id=request_data.conversation_id)
        if not conversation_out:
            logger.info(
                create_info_logging_message(
                    endpoint="/message",
                    feature="send_message_to_chatbot",
                    message=str(ConversationResponses.INVALID_CONVERSATION_ID["message"]),
                )
            )
            return JSONResponse(
                status_code=200,
                content=APIResponse(
                    **ConversationResponses.INVALID_CONVERSATION_ID,
                    data={},
                ),
            )
    try:
        user_role = crud.role.get(db=db, id=current_user.role_id).name
        if not conversation_out:
            # Create conversation in Conversation table
            conversation_in = schemas.ConversationInDB(
                user_id=current_user.id,
                course_id=request_data.course_id,
                activity_id=request_data.activity_id,
                screen=request_data.screen,
            )
            conversation_out = crud.conversation.create(db=db, obj_in=conversation_in)
        
            logger.info(
                create_info_logging_message(
                    endpoint="/message",
                    feature="send_message_to_chatbot",
                    message=str(ConversationResponses.CREATE_SUCCESS["message"]),
                )
            )
        message_in = schemas.MessageInDB(
                conversation_id=conversation_out.id,
                message=request_data.message,
                sender="user",
            )
        message_out = crud.message.create(db=db, obj_in=message_in)
        chatbot_response = get_response_from_chatbot(request_data.message, user_role)
        message_in = schemas.MessageInDB(
            conversation_id=conversation_out.id,
            message=chatbot_response,
            sender="chatbot",
        )
        message_out = crud.message.create(db=db, obj_in=message_in)
        
        # Update prompts count
        conversation_out.prompts_count = conversation_out.prompts_count + 1
        crud.conversation.update(db=db, db_obj=conversation_out, obj_in=conversation_out.__dict__)
        
        # Get total user count
        conversation_out.total_user_prompts = crud.conversation.get_conversation_count_by_user(db=db, user_id=current_user.id)
        
        conversation_out.chatbot_response = message_out
        conversation = schemas.ConversationReturn.from_orm(conversation_out)
        return JSONResponse(
                status_code=200,
                content=APIResponse(
                    **ConversationResponses.CREATE_SUCCESS,
                    data=json.loads(conversation.json()),
                ),
            )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
