"""
This file contains the model mapping of
user table
"""

from datetime import datetime

from app.db.base_class import Base

from sqlalchemy import (
    Column,
    DateTime,
    Date,
    ForeignKey,
    Integer,
    String,
)
from sqlalchemy_utils import EmailType
from sqlalchemy.orm import relationship


class User(Base):
    name = Column(
        String(100), comment="user first name", nullable=False
    )
    last_name = Column(
        String(100), comment="user last name", default="", nullable=True
    )
    IC = Column(
        String(40), comment="Identity Card Number"
    )
    email = Column(EmailType, unique=False, nullable=False)
    date_of_birth = Column(Date, default=None, nullable=True)
    role_id = Column(
        Integer, ForeignKey("role.id", ondelete="RESTRICT"), nullable=False, index=True
    )
    cohort_id = Column(
        Integer, ForeignKey("cohort.id", ondelete="RESTRICT"), nullable=True, index=True
    )
    ethnicity = Column(String(40), comment="Race of user", nullable=False, default="others")
    profile_picture = Column(String(300), comment="profile picture file path", default=None)
    hashed_password = Column(String(256), nullable=False)
    timezone = Column(String(200), default="")
    language = Column(String(100), default="")
    # Legacy organization fields - kept for backward compatibility
    organization_name = Column(String(100), default="")
    role_in_organization = Column(String(100), default="")
    organization_type = Column(String(100), default="")
    # New organization relationship
    organization_id = Column(
        Integer, ForeignKey("organization.id", ondelete="RESTRICT"), nullable=True, index=True
    )
    organization = relationship("Organization", back_populates="users")
    courses_progress = relationship("CourseProgress", lazy="joined")
    heygen_videos = relationship("HeygenVideo", back_populates="instructor")
    last_login = Column(DateTime, nullable=True)
    created_on = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

