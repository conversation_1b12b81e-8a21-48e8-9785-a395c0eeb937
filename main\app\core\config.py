import pathlib
from typing import List, Union

from pydantic import AnyHttpUrl, BaseSettings, EmailStr, validator

# Project Directories
ROOT_DIR: pathlib.Path = pathlib.Path(__file__).resolve().parent.parent


class DBSettings(BaseSettings):
    """Database Settings"""

    DATABASE_CONNECTION_URL: str = ""
    DATABASE_HOST: str = ""
    DATABASE_PORT: str = ""
    DATABASE_USERNAME: str = ""
    DATABASE_PASSWORD: str = ""
    DATABASE_NAME: str = ""
    
    class Config:
        env_file = ".env"


class EmailSettings(BaseSettings):
    """Email Settings"""

    EMAIL_FROM_EMAIL: str = ""
    EMAIL_FROM_NAME: str = ""
    EMAIL_SMTP_HOST: str = ""
    EMAIL_SMTP_PORT: int = 0
    EMAIL_SMTP_USERNAME: str = ""
    EMAIL_SMTP_PASSWORD: str = ""
    EMAIL_SMTP_TLS: bool = True
    EMAIL_SMTP_SSL: bool = True
    
    class Config:
        env_file = ".env"


class JWTSettings(BaseSettings):
    """JWT Settings"""

    JWT_SECRET_KEY: str = ""
    ACCESS_TOKEN_EXPIRY_MINUTES: int = 60
    REFRESH_TOKEN_EXPIRY_MINUTES: int = 60 * 24
    JWT_ALGORITHM: str = ""
    
    class Config:
        env_file = ".env"


class OpenAISettings(BaseSettings):
    """OpenAI Settings"""
    
    OUR_OPENAI_API_KEY: str = ""
    
    class Config:
        env_file = ".env"


class BaseConfig(BaseSettings):
    """Base Configuration"""

    pass


class Settings(BaseSettings):
    """Settings"""

    # Read default settings from environment
    # Overwrite them with default values if undefined
    API_V1_STR: str = "/api/v1"
    BACKEND_CORS_ORIGINS: List[str | AnyHttpUrl] = ["*"]

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    @classmethod
    # Second argument v: can be either JSON formatted list of URLs, or simply comma separated
    def check_backend_cors_origins(
        cls, v: Union[str, List[str]]
    ) -> Union[str, List[str]]:
        # If v fetched from environment variables
        if isinstance(v, str) and (not v.startswith("[")):
            return [x.strip() for x in v.split(",")]
        elif isinstance(v, list):
            return v
        return ValueError(v)
    
    db = DBSettings()
    db.DATABASE_CONNECTION_URL = db.DATABASE_CONNECTION_URL.format(**db.dict())


    email = EmailSettings()

    jwt = JWTSettings()
    openai = OpenAISettings()
    
    HEYGEN_API_KEY: str = ""
    HEYGEN_BASE_URL: str = "https://api.heygen.com"
    
    class Config:
        env_file = ".env"


settings = Settings()
