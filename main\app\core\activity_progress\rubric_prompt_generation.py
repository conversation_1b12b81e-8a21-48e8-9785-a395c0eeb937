import math

SAMPLE_RESPONSE_STRUCT = {
    "criteria name 1": {
        "question 1": {
            "explaination": "explaination for giving the score to that question"
        },
        "question 2": {
            "explaination": "explaination for giving the score to that question"
        },
        "sub total": "obtained score for complete criteria in integer / total questions in criteria",
    },
    "criteria name 2": {
        "question 1": {
            "explaination": "explaination for giving the score to that question"
        },
        "question 2": {
            "explaination": "explaination for giving the score to that question"
        },
        "sub total": "obtained score for complete criteria in integer / total questions in criteria",
    },
    "total score": "obtained score for all criterias combined/ total points",
    "suggestion for improvents": "suggestions here"
}

# Generic evaluation prompt template
base_prompt_template = """You are an AI evaluator reviewing a transcript from a roleplay interaction. The scenario may vary, but your task is to evaluate the learner's (Trainee's) communication, decision-making, and interpersonal performance using a rubric composed of customizable categories and assessment questions.

**IMPORTANT**: Always address the learner directly using second person ("you"). For example, say "you stayed calm," not "the learner stayed calm." Make the feedback personal and direct.

### Context:
The learner participated in a scenario-based training conversation with an AI avatar. Each category in the rubric contains several sub-criteria related to essential skills that will be evaluated based on the provided criteria.

Your job is to evaluate each **sub-criterion individually**, and provide a clear explanation of whether and how the learner demonstrated that skill. Each sub-criterion is worth **1 point**. Then, calculate a **total score per category**, and finally, the **overall score** based on the total number of points achieved versus total possible points.

---

### Evaluation Rubric:

**REMINDER**: Throughout your entire evaluation, ALWAYS use "you" when referring to the learner. Write all explanations and feedback as if you are speaking directly to the person being evaluated.

Each section includes multiple sub-criteria (assessment questions). For each, score as follows:
- 1 = Demonstrated  
- 0 = Not Demonstrated

{dynamic_criteria}

---

### Final Score Summary:

**Total Score:** X/{total_points}
**Score Range Interpretation: **IMPORTANT**: Always address the learner directly using second person ("you")**
{score_interpretation}

---

**Suggestions for Improvement:**
[**CRITICAL REQUIREMENT**: You MUST address the learner directly using "you" in ALL suggestions. NEVER use "trainee", "the learner", "they", "he", "she" or any third-person references. Always write as if speaking directly to the person being evaluated.]

[Example: Instead of "The trainee should smile more" write "You should smile more"]
[Example: Instead of "The learner needs to improve eye contact" write "You need to improve eye contact"]

[If total score is below the top range, provide actionable, second-person feedback based on the specific criteria that were not met. Only include feedback based on what is visible in the video frames — do not infer unspoken actions.]

---


Only evaluate what is present in the dialogue. Do not speculate or add assumptions. Focus solely on the trainee's communication behaviors, control, and professionalism within the roleplay scenario."""


def generate_dynamic_evaluation_prompt(criteria, score_interpretation=None):
    """
    Generate dynamic evaluation prompt based on user criteria
    
    Args:
        criteria: List of criterion dictionaries
        Each criterion should have:
        {
            'name': 'Criterion Name',
            'assessmentQuestions': [
                'Assessment question text 1',
                'Assessment question text 2'
            ]
        }
        
        score_interpretation: Optional custom score interpretation ranges
    
    Returns:
        List with single dictionary containing role and content for AI system
    """
    criteria = convert_form_criteria_to_evaluation_format(criteria)
    if not criteria or not isinstance(criteria, list) or len(criteria) == 0:
        raise ValueError('Criteria must be a non-empty list')
    
    dynamic_criteria = ""
    total_points = 0
    criterion_number = 1
    
    # Generate dynamic criteria sections
    for criterion in criteria:
        if not isinstance(criterion, dict) or 'name' not in criterion or 'assessmentQuestions' not in criterion:
            raise ValueError(f'Criterion {criterion_number} must have a name and assessmentQuestions list')
        
        if not isinstance(criterion['assessmentQuestions'], list):
            raise ValueError(f'Criterion {criterion_number} assessmentQuestions must be a list')
        
        if len(criterion['assessmentQuestions']) == 0:
            raise ValueError(f'Criterion {criterion_number} must have at least one assessment question')
        
        dynamic_criteria += f"#### Criterion {criterion_number}: {criterion['name']}\n"
        
        # Add assessment questions for this criterion
        for i, question in enumerate(criterion['assessmentQuestions']):
            if not question or not isinstance(question, str) or question.strip() == '':
                raise ValueError(f'Assessment question {i + 1} in Criterion {criterion_number} must be a non-empty string')
            
            # Extract question name from the beginning of the text (before colon if exists)
            question_parts = question.split(':')
            question_name = question_parts[0].strip()
            question_text = question.strip()
            
            dynamic_criteria += f"- **{question_name}**\n"
            dynamic_criteria += f"  Score: X/1\n"
            dynamic_criteria += f"  Explanation: <{question_text}>\n\n"
            total_points += 1
        
        dynamic_criteria += f"**Subtotal for Criterion {criterion_number}: X/{len(criterion['assessmentQuestions'])}**\n\n"
        criterion_number += 1
    
    # Default score interpretation if not provided
    default_score_interpretation = generate_default_score_interpretation(total_points)
    final_score_interpretation = score_interpretation or default_score_interpretation
    
    # Replace placeholders in template
    final_prompt = base_prompt_template.format(
        dynamic_criteria=dynamic_criteria.strip(),
        total_points=str(total_points),
        score_interpretation=final_score_interpretation
    )
    
    return {
            "role": "system",
            "content": final_prompt
        }
        
    


def generate_default_score_interpretation(total_points):
    """
    Helper function to generate default score interpretation
    """
    excellent = math.ceil(total_points * 0.85)  # 85% and above
    competent = math.ceil(total_points * 0.65)   # 65-84%
    developing = math.ceil(total_points * 0.35)  # 35-64%
    
    return f"""- {excellent}-{total_points}: Excellent - Highly effective performance across all criteria
    - {competent}-{excellent - 1}: Competent - Strong performance with minor areas for improvement
    - {developing}-{competent - 1}: Developing - Shows effort but needs significant improvement
    - 0-{developing - 1}: Needs Significant Support - Key competencies not demonstrated"""

def convert_form_criteria_to_evaluation_format(form_criteria):
    """
    Convert form criteria structure to the format expected by generate_dynamic_evaluation_prompt
    
    Form structure:
    {
      "name": "criterion name",
      "questions": [{"question": "question text"}]
    }
    
    Expected structure:
    {
      "name": "criterion name", 
      "assessmentQuestions": ["question text 1", "question text 2"]
    }
    """
    converted_criteria = []
    
    for criterion in form_criteria:
        converted_criterion = {
            'name': criterion['name'],
            'assessmentQuestions': []
        }
        
        # Extract question text from each question object
        for question_obj in criterion['questions']:
            question_text = question_obj['question'].strip()  # Remove any extra whitespace/newlines
            converted_criterion['assessmentQuestions'].append(question_text)
        
        converted_criteria.append(converted_criterion)
    
    return converted_criteria
