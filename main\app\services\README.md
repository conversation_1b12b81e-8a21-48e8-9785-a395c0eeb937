# Application Services

A collection of services that add domain functionalities to the application.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)
   1. [`email` Service Interface](#email-service-interface)
   
## Description

A service is a module that provide functionalities with complex domain logic. These should be able to be deployed independently from this application as standalone services. For example, caching or email capabilities are functionalities that can be deployed independently from the current application. (It doesn't mean we are going to deploy it this way, it's just to say that service code should have low decoupling and any dependencies should be injected).

If an API endpoint's domain logic is complex, it should be decoupled off into a service module.

**However**, service modules is used to organize domain logic, **NOT** database transactions. If your endpoint's logic is mostly transactions, then it should exist in the `crud` directory as a transaction method.

## Usage

A service module is way to organize and decouple domain logic. If the domain logic is not too complex, then just one module file is sufficient. If it is very complex with a lot of layers and moving parts, then a new directory can be created to contain all related components.

A service must clearly define ways for the client to interact with it by exposing explicitly interface methods while hiding the rest of the low-level implementation.

The table below show all available services with the corresponding entrypoint files. An entrypoint file contain the interface methods of the service module. The client should import and call interface methods in this file in order to gain access to the domain functionality.

| Service            | Entrypoint File                        | Purpose                                                                                                                                   |
| ------------------ | -------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------- |
| `email`            | `services/email.py`                    | Send emails. Currently wrapping around `smtplib`.                                                                                         |                                                                                            |

### `email` Service Interface

For more information on how to use `smtplib`, check out the [official documentation](https://docs.python.org/3/library/smtplib.html).

| Module              | Method       | Purpose        |
| ------------------- | ------------ | -------------- |
| `email.EmailClient` | `send_email` | Send an email. |
