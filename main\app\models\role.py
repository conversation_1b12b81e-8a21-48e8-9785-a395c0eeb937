"""
This file contains the model mapping of 
role table
"""

from datetime import datetime

from app.db.base_class import Base

from sqlalchemy import Column, DateTime, String


class Role(Base):
    name = Column(String(50), unique=True, nullable=False)
    created_on = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated = Column(
        DateTime, default=datetime.now, onupdate=datetime.utcnow, nullable=False
    )
