services:
  database:
    container_name: ial-database
    image: postgres:12
    restart: always
    volumes:
      - /data/database_volume:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=testuser
      - POSTGRES_PASSWORD=test123
      - POSTGRES_DB=testdb

  # web container, with django + gunicorn
  ial-backend:
    image: ial-backend:latest
    build:
      context: .
      dockerfile: ./Dockerfile
    restart: always
    command: gunicorn app.main:app --reload --workers 2 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:5000 --timeout 150
    environment:
      - APP_ENVIRONMENT=development
      - EMAIL_FROM_EMAIL=<EMAIL>
      - EMAIL_FROM_NAME=No-Reply Mandai
      - EMAIL_SMTP_HOST=smtp.gmail.com
      - EMAIL_SMTP_PORT=587
      - EMAIL_SMTP_USERNAME=<EMAIL>
      - EMAIL_SMTP_PASSWORD=dujittwvobvwxktm
      - EMAIL_SMTP_TLS=True
      - EMAIL_SMTP_SSL=True
      - SWAGGER_USERNAME=IAL-dev
      - SWAGGER_PASSWORD=ialDev
      - DATABASE_CONNECTION_URL=*******************************************/gem_db
      #      - DATABASE_CONNECTION_URL=postgresql://ial_db_master:<EMAIL>:5432/gem_db
      - JWT_SECRET_KEY=eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6MTcxMzAzNzA1NywiaWF0IjoxNzEzMDM3MDU3fQ.ki25CwlBoUhjkMMGs6X-y1xrbN_715Fb2f35C5x5lHs
      - JWT_ALGORITHM=HS256
      - OUR_OPENAI_API_KEY=***************************************************
      - ACCESS_TOKEN_EXPIRY_MINUTES=10080
      - REFRESH_TOKEN_EXPIRY_MINUTES=10080
      #      - AWS_ACCESS_KEY_ID=********************
      #      - AWS_SECRET_ACCESS_KEY=cYyeSGqOn79dlFDAc0VdlLCe+PHO+9PxSf9/q4dY
      - AWS_ACCESS_KEY_ID=********************
      - AWS_SECRET_ACCESS_KEY=Ucxe81wwJ/4QQFSKoGC5F5YviBDKl5bATAUxPvIF
      - AWS_BUCKET_NAME=stagingial
      - HEYGEN_API_KEY=ZWI2ZWVhZmM2NWM4NDU4ZGE1ZTZhYzNlMDlkODNiMGEtMTc0NTgyOTk1OQ==
    ports:
      - "8010:5000"
    #volumes:
    #  - /tmp:/tmp
    #   - /data/media:/app/media
    #   - /data/log_files:/app/log_files
    #   - ./initial_data.json:/app/initial_data.json
    depends_on:
      - database

  ial-frontend:
    image: ial-frontend:latest
    build:
      context: ../IAL-Frontend
      dockerfile: ./dockerfile
    restart: always
    env_file:
      - ../IAL-Frontend/.env
    ports:
      - "8080:3000"
