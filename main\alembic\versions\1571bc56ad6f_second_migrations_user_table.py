"""Second migrations-User table

Revision ID: 1571bc56ad6f
Revises: ac8d0c4fb117
Create Date: 2024-09-03 00:21:11.658261

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1571bc56ad6f'
down_revision = 'ac8d0c4fb117'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('last_name', sa.String(length=100), nullable=True, comment='user last name'))
    op.add_column('user', sa.Column('date_of_birth', sa.Date(), nullable=True))
    op.add_column('user', sa.Column('organization_name', sa.String(length=100), nullable=True))
    op.add_column('user', sa.Column('role_in_organization', sa.String(length=100), nullable=True))
    op.add_column('user', sa.Column('organization_type', sa.String(length=100), nullable=True))
    op.alter_column('user', 'name',
               existing_type=sa.VARCHAR(length=40),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('user', 'IC',
               existing_type=sa.VARCHAR(length=40),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user', 'IC',
               existing_type=sa.VARCHAR(length=40),
               nullable=False)
    op.alter_column('user', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=40),
               existing_nullable=False)
    op.drop_column('user', 'organization_type')
    op.drop_column('user', 'role_in_organization')
    op.drop_column('user', 'organization_name')
    op.drop_column('user', 'date_of_birth')
    op.drop_column('user', 'last_name')
    # ### end Alembic commands ###
