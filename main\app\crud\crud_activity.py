"""
This file contains all the crud operations for activity
"""

from typing import Optional

from app.crud.base import CRUDBase
from app.models.activity import Activity
from app.schemas.activity import ActivityCreate, ActivityUpdate

from sqlalchemy.orm import Session


class CRUDActivity(CRUDBase[Activity, ActivityCreate, ActivityUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Activity]:
        return db.query(Activity).filter(Activity.name == name).first()
    
    def get_by_type(self, db: Session, *, type: str) -> Optional[Activity]:
        return db.query(Activity).filter(Activity.type == type).first()

    def get_by_id(self, db: Session, *, id: int) -> Optional[Activity]:
        return db.query(Activity).filter(Activity.id == id).first()

    def get_all_activities(self, db: Session) -> list[Activity]:
        return db.query(Activity).all()


activity = CRUDActivity(Activity)
