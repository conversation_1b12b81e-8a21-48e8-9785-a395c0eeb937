# OAuth2 Action Scopes

A collection of objects that contain OAuth2 scopes that is associated to the actions that a user can perform.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)
3. [Example](#example)

## Description

A scope is a string label that is associated with an action can be performed on a particular entity. A scope object contain labels for all available actions. Scope objects are organized into separate modules based on entity. A user is assigned a list of scopes, each of which corresponds to what they can or can't do. This is a similar concept to an ACL.

Scope is part of the OAuth2 spec. Checkout the following documentation for more information:

- [Fastapi's tutorial that talk about scopes](https://fastapi.tiangolo.com/tutorial/security/simple-oauth2/)
- [OAuth2 scope introduction](https://www.oauth.com/oauth2-servers/scope/)
- [OAuth2 scope definition](https://www.oauth.com/oauth2-servers/scope/defining-scopes/)
- [An example of GitHub's scopes](https://docs.github.com/en/apps/oauth-apps/building-oauth-apps/scopes-for-oauth-apps)
- [Google Cloud's OAuth2 scope tutorial](https://docs.github.com/en/apps/oauth-apps/building-oauth-apps/scopes-for-oauth-apps)

## Usage

For each action on an entity, there should be a corresponding scope label string. This should be put into the appropriate scope module that corresponds to that specific entity. This label can, then, be assigned to different users depending on their privilege level.

The scope label must have the following format:

`<entity>:<context>:<action>`

## Example

The code below is an example of `User` scopes. If a user have `invite` in their scope list, then they are authorized to create/Invite a `User` enity. This concept applies to the rest of the scopes.

```python
# scopes/user.py

from pydantic import BaseModel


class UserScopesClass(BaseModel):
    me: str = "user:me"
    read: str = "user:read"
    update: str = "user:update"
    delete: str = "user:delete"
    invite: str = "user:invite"


UserScopes = UserScopesClass()
```

Import these to the `__init__.py` to help the rest of the application import from `scopes`:

```python
# scopes/__init__.py

from .user import UserScopes
```

For each API endpoint, we can assign a list of scopes to it so that only users that have those scopes can invoke it. An example interface method for the `User` creation endpoint is shown below:

```python
# endpoints/user.py

from fastapi import Depends, Security
from fastapi.encoders import jsonable_encoder
from starlette.responses import JSONResponse

from app.api import deps
from app import models, schemas
from app.scopes import UserScopes


@router.post("/invite")
def user_invite(
    *,
    background_tasks: BackgroundTasks,
    request_data: schemas.UserInvite,
    db: Session = Depends(deps.get_db),
    email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.invite]
    ),
) -> JSONResponse:
    pass
```

The key part in that method is the `current_user` argument. Notice how it is tied to fastapi's `Security`, which takes in the current user model along with a list of scopes. What this does is basically to allow only the users that have this list of scopes call this endpoint. From the list, this endpoint requires a user to have the following scopes:

- `UserScopes.invite` (_user:invite_)
