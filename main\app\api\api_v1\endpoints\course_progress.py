"""
This file contains all the API's related to course
"""

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.services import EmailClient
from app.core.security import get_password_hash, verify_password
from app.scopes import CourseScopes, CourseProgressScopes, AuthScopes
from app.responses import CourseResponses, CourseProgressResponses, GeneralResponses, AuthResponses, RoleResponses
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from typing import List

router = APIRouter()


def enroll_multiple_users(db: Session, course_progress_list: List[schemas.CourseProgressRequestMultiple]):
    for course_progress in course_progress_list:
        course_id = course_progress.course_id
        course = crud.course.get(db=db, id=course_id)
        # Verify if user exists in system
        if course:
            course_progress_db = crud.course_progress.get_by_user_and_course(
                db=db, user_id=course_progress.user_id, course_id=course_id)
        if not course_progress_db:
            # Create course in Course table
            course_progress_in = schemas.CourseProgressCreate(
                course_id=course_id,
                user_id=course_progress.user_id
            )
            crud.course_progress.create(db=db, obj_in=course_progress_in)


@router.post(
    "/create",
    status_code=status.HTTP_200_OK,
    response_model=schemas.CourseReturn
)
def enroll_in_course(
    *,
    # background_tasks: BackgroundTasks,
    request_data: schemas.CourseProgressRequest,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseProgressScopes.create]
    ),
) -> JSONResponse:
    """Enroll user in a new course by creating course progress

    Args:\n
        request_data


    """

    try:
        course_id = request_data.course_id
        course = crud.course.get(db=db, id=course_id)
        # Verify if user exists in system
        if not course:
            logger.info(
                create_info_logging_message(
                    endpoint="/create",
                    feature="course_progress",
                    message=str(CourseResponses.COURSE_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(**CourseResponses.COURSE_NOT_EXIST),
            )

        course_progress = crud.course_progress.get_by_user_and_course(
            db=db, user_id=current_user.id, course_id=course_id)
        if course_progress:
            logger.info(
                create_info_logging_message(
                    endpoint="/create",
                    feature="course_progress",
                    message=str(
                        CourseProgressResponses.ALREADY_ENROLLED["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(
                    **CourseProgressResponses.ALREADY_ENROLLED),
            )
        # Create course in Course table
        course_progress_in = schemas.CourseProgressCreate(
            course_id=course_id,
            user_id=current_user.id
        )
        course_progress_out = crud.course_progress.create(
            db=db, obj_in=course_progress_in)

        # Update User in the DB
        # background_tasks.add_task(
        #     crud.course.update(db=db, db_obj=course, obj_in=course_update_in)
        # )

        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="course_progress",
                message=str(
                    CourseProgressResponses.SUCCESSFULLY_ENROLLED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **CourseProgressResponses.SUCCESSFULLY_ENROLLED,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
        
        

@router.post(
    "/create-multiple",
    status_code=status.HTTP_200_OK,
    response_model=schemas.CourseReturn
)
def enroll_multiple_users_in_different_courses(
    *,
    background_tasks: BackgroundTasks,
    request_data: List[schemas.CourseProgressRequestMultiple],
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseProgressScopes.create]
    ),
) -> JSONResponse:
    """Enroll user in a new course by creating course progress

    Args:\n
        request_data


    """

    try:
        # Update Course Progress in the DB
        background_tasks.add_task(
            enroll_multiple_users(db=db, course_progress_list=request_data)
        )

        logger.info(
            create_info_logging_message(
                endpoint="/create-multiple",
                feature="course_progress",
                message=str(
                    CourseProgressResponses.SUCCESSFULLY_ENROLLED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **CourseProgressResponses.SUCCESSFULLY_ENROLLED,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
        
        
@router.post(
    "/create-by-cohort",
    status_code=status.HTTP_200_OK,
    response_model=schemas.CourseReturn
)
def enroll_multiple_users_in_different_courses(
    *,
    background_tasks: BackgroundTasks,
    request_data: schemas.CourseProgressCohortRequest,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseProgressScopes.create]
    ),
) -> JSONResponse:
    """Enroll user in a new course by creating course progress

    Args:\n
        request_data


    """

    try:
        user_ids = crud.user.get_cohort_user_id_list(db=db, cohort_id=request_data.cohort_id)
        course_progress_list = [schemas.CourseProgressRequestMultiple(**{"user_id": user_id, "course_id": request_data.course_id})
                                for (user_id,) in user_ids]
        # Update Course Progress in the DB
        background_tasks.add_task(
            enroll_multiple_users(db=db, course_progress_list=course_progress_list)
        )

        logger.info(
            create_info_logging_message(
                endpoint="/create-multiple",
                feature="course_progress",
                message=str(
                    CourseProgressResponses.SUCCESSFULLY_ENROLLED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **CourseProgressResponses.SUCCESSFULLY_ENROLLED,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


# @router.patch(
#     "/update/{course_id}",
#     status_code=status.HTTP_200_OK,
#     response_model=schemas.CourseReturn
# )
def course_update(
    *,
    course_id: int,
    course_update_in: schemas.CourseUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    # email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseScopes.update]
    ),
) -> JSONResponse:
    """Update user data

    Args:\n
        request_data


    """
    try:
        course = crud.course.get(db=db, id=course_id)
        # Verify if user exists in system
        if not course:
            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="course",
                    message=str(CourseResponses.COURSE_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(**CourseResponses.COURSE_NOT_EXIST),
            )

        # Update User in the DB
        background_tasks.add_task(
            crud.course.update(db=db, db_obj=course, obj_in=course_update_in)
        )

        logger.info(
            create_info_logging_message(
                endpoint="/update",
                feature="course",
                message=str(CourseResponses.COURSE_UPDATED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **CourseResponses.COURSE_UPDATED,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


# @router.delete(
#     "/delete/{course_id}",
#     status_code=status.HTTP_200_OK,
#     response_model=schemas.CourseReturn
# )
def course_delete(
    *,
    course_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseScopes.delete]
    ),
) -> JSONResponse:
    """Delete course data

    Args:\n
        request_data


    """
    try:
        course = crud.course.get(db=db, id=course_id)
        # Verify if user exists in system
        if not course:
            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="course",
                    message=str(CourseResponses.COURSE_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(**CourseResponses.COURSE_NOT_EXIST),
            )

        # Update User in the DB
        background_tasks.add_task(crud.course.remove(db=db, id=course_id))

        logger.info(
            create_info_logging_message(
                endpoint="/delete",
                feature="course",
                message=str(CourseResponses.COURSE_DELETED_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **CourseResponses.COURSE_DELETED_SUCCESS,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/get/all",
    status_code=status.HTTP_200_OK,
    response_model=List[schemas.CourseProgressReturn]
)
def get_all_enrolled_courses(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseProgressScopes.read]
    ),
) -> JSONResponse:
    """Get all enrolled courses of learner with progress

    Args:\n
        request_data


    """
    try:
        # course_progress = crud.course_progress.get_by_user_id(db=db, user_id=current_user.id)
        course_progress_list = []
        for course_progress in crud.course_progress.get_by_user_id(db=db, user_id=current_user.id):
            course = crud.course.get(
                db=db, id=course_progress.course_id).__dict__
            activities = []
            db_activities = db.query(models.CourseActivityAssociation).filter(models.CourseActivityAssociation.course_id ==
                                                                              course_progress.course_id).all()
            a_progress_ids = crud.activity_progress.get_completed_activities_of_user(db=db, user_id=current_user.id,
                                                                                     course_id=course_progress.course_id)
            a_progress_ids = [a[0] for a in a_progress_ids]
            for csa in db_activities:
                activity = crud.activity.get(db=db, id=csa.activity_id)
                activities.append({
                    'id': activity.id,
                    'name': activity.name,
                    'type': activity.type,
                    'sequence': csa.sequence,
                    'completed': activity.id in a_progress_ids
                })
            course['activities'] = sorted(
                activities, key=lambda x: x["sequence"])
            course_progress_dict = course_progress.__dict__
            course_progress_dict['course'] = course
            course_progress_list.append(course_progress_dict)
        return course_progress_list
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/enrolled-users/{course_id}",
    status_code=status.HTTP_200_OK,
    response_model=Page[schemas.UserOut]
)
def get_enrolled_users(
    course_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseProgressScopes.read]
    ),
) -> JSONResponse:
    """Get all users enrolled in a specific course

    Args:
        course_id: ID of the course to get enrolled users for

    Returns:
        Paginated list of enrolled users
    """
    try:
        return paginate(
            db.query(models.User)\
                .join(models.CourseProgress, models.User.id == models.CourseProgress.user_id)\
                .filter(models.CourseProgress.course_id == course_id)\
                .order_by(models.User.id)
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
