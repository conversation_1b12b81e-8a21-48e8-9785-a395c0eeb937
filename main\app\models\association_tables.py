from sqlalchemy import create_engine, <PERSON>umn, Integer, ForeignKey, DateTime
from sqlalchemy.ext.declarative import declarative_base

from app.db.base_class import Base
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

# Association Table to store the many-to-many relationship with sequence
class CourseActivityAssociation(Base):
    __tablename__ = 'course_activity_association'

    course_id = Column(Integer, ForeignKey('course.id'))
    activity_id = Column(Integer, ForeignKey('activity.id'))
    sequence = Column(Integer)
    
