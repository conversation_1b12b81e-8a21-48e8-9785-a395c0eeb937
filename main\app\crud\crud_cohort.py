"""
This file contains all the crud operations for 
role related things
"""

from typing import Optional

from app.crud.base import CRUDBase
from app.models.cohort import Cohort
from app.schemas.cohort import CohortCreate, CohortUpdate

from sqlalchemy.orm import Session


class CRUDCohort(CRUDBase[Cohort, CohortCreate, CohortUpdate]):
    def get_by_name(self, db: Session, name: str) -> Optional[Cohort]:
        try:
            return db.query(Cohort).filter(Cohort.name == name).first()
        except:
            return None

    def get_by_file_hash(self, db: Session, file_hash: str) -> Optional[Cohort]:
        try:
            return db.query(Cohort).filter(Cohort.file_hash == file_hash).first()
        except:
            return None

    def get_by_id(self, db: Session, id: int) -> Optional[Cohort]:
        return db.query(Cohort).filter(Cohort.id == id).first()

    def get_all_cohorts(self, db: Session) -> list[Cohort]:
        return db.query(Cohort).all()



cohort = CRUDCohort(Cohort)
