"""
<PERSON><PERSON><PERSON> to migrate existing user data to the new organization structure.
This script:
1. Creates organizations based on existing organization_name values in the user table
2. Links users to their respective organizations
3. Sets all organizations to 'approved' status by default
"""
import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

print(f'current working directory: {os.getcwd()}')
from app.db.session import SessionLocal
from app import models
from app.models.organization import OrganizationStatus
from sqlalchemy.orm import Session
from sqlalchemy import distinct
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_unique_organization_names(db: Session) -> list:
    """Get all unique organization names from the user table"""
    # Query distinct organization names that are not empty
    org_names = db.query(distinct(models.User.organization_name))\
                  .filter(models.User.organization_name != "")\
                  .filter(models.User.organization_name.isnot(None))\
                  .all()
    # Flatten the result
    return [name[0] for name in org_names if name[0]]

def create_organizations(db: Session, org_names: list) -> dict:
    """Create organizations for each unique organization name"""
    org_map = {}  # Map organization names to organization IDs
    
    for name in org_names:
        # Check if organization already exists
        existing_org = db.query(models.Organization)\
                         .filter(models.Organization.name == name)\
                         .first()
        
        if existing_org:
            logger.info(f"Organization '{name}' already exists with ID {existing_org.id}")
            org_map[name] = existing_org.id
            continue
        
        # Create new organization
        new_org = models.Organization(
            name=name,
            status=OrganizationStatus.APPROVED,  # Set all existing organizations to approved
            description=f"Auto-created organization from migration for {name}"
        )
        db.add(new_org)
        db.flush()  # Flush to get the ID
        
        logger.info(f"Created organization '{name}' with ID {new_org.id}")
        org_map[name] = new_org.id
    
    db.commit()
    return org_map

def link_users_to_organizations(db: Session, org_map: dict) -> None:
    """Link users to their respective organizations"""
    for org_name, org_id in org_map.items():
        # Find users with this organization name
        users = db.query(models.User)\
                  .filter(models.User.organization_name == org_name)\
                  .all()
        
        if not users:
            logger.warning(f"No users found for organization '{org_name}'")
            continue
        
        # Update users to link to the organization
        for user in users:
            user.organization_id = org_id
        
        logger.info(f"Linked {len(users)} users to organization '{org_name}' (ID: {org_id})")
    
    db.commit()

def migrate_data():
    """Main migration function"""
    db = SessionLocal()
    try:
        logger.info("Starting organization data migration")
        
        # Get unique organization names
        org_names = get_unique_organization_names(db)
        logger.info(f"Found {len(org_names)} unique organization names")
        
        if not org_names:
            logger.info("No organizations to migrate")
            return
        
        # Create organizations
        org_map = create_organizations(db, org_names)
        
        # Link users to organizations
        link_users_to_organizations(db, org_map)
        
        logger.info("Organization data migration completed successfully")
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error during migration: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    
    migrate_data()
