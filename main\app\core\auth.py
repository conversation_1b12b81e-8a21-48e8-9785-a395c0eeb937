from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional, <PERSON><PERSON>

from app import models
from app.core.config import settings

from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2AuthorizationCodeBearer
from jose import jwt
from pydantic import validate_arguments
from sqlalchemy.orm import Session

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/swagger_ui")


@validate_arguments(config=dict(arbitrary_types_allowed=True))
def authenticate(
    *,
    username: str,
    db: Session,
) -> Optional[models.User]:
    """Authenticates a user by email and password.

    Args:
        username (str): The username of the user.
        db (Session): The database session.
        username_type: email

    Returns:
        User: The user if authenticated, None otherwise.
    """
    # initialize the relation and filters variables

    
    # this will add filter email according to passed username_type
    return db.query(models.User).filter(models.User.email == username).first()



@validate_arguments
def create_jwt_token(data: dict) -> <PERSON><PERSON>[str, str]:
    """Creates a JWT token.

    Args:
        data (dict): The data to be encoded in the token.

    Returns:
        Tuple[str, str]: The access and refresh tokens.
    """

    data["sub"] = str(data["sub"])

    # Copy data to access and refresh
    access_encode = data.copy()
    refresh_encode = data.copy()

    # Remove all scopes from refresh token
    refresh_encode["scopes"] = []

    # Set types of tokens
    access_encode["type"] = "access"
    refresh_encode["type"] = "refresh"

    # Set token expiration time
    access_encode["exp"] = datetime.utcnow() + timedelta(
        minutes=settings.jwt.ACCESS_TOKEN_EXPIRY_MINUTES
    )
    refresh_encode["exp"] = datetime.utcnow() + timedelta(
        minutes=settings.jwt.REFRESH_TOKEN_EXPIRY_MINUTES
    )

    # Encode to JWT
    access_token = jwt.encode(
        access_encode, settings.jwt.JWT_SECRET_KEY, algorithm=settings.jwt.JWT_ALGORITHM
    )
    refresh_token = jwt.encode(
        refresh_encode,
        settings.jwt.JWT_SECRET_KEY,
        algorithm=settings.jwt.JWT_ALGORITHM,
    )

    return access_token, refresh_token
