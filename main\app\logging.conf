[loggers]
keys=root,uicheckapp

[handlers]
keys=consoleH<PERSON>ler,detailedConsoleHandler

[formatters]
keys=normalFormatter,detailedFormatter

[logger_root]
level=INFO
handlers=consoleHandler

[logger_uicheckapp]
level=DEBUG
handlers=detailedConsoleHandler
qualname=uicheckapp
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=normalFormatter
args=(sys.stdout,)

[handler_detailedConsoleHandler]
class=StreamHandler
level=DEBUG
formatter=detailedFormatter
args=(sys.stdout,)

[formatter_normalFormatter]
format=%(asctime)s loglevel=%(levelname)-6s logger=%(name)s %(funcName)s() L%(lineno)-4d %(message)s

[formatter_detailedFormatter]
format=%(asctime)s loglevel=%(levelname)-6s logger=%(name)s %(funcName)s() L%(lineno)-4d %(message)s   call_trace=%(pathname)s L%(lineno)-4d

