import httpx

from fastapi import APIRouter, Security, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from app.api import deps
from app.core.config import settings
from app.models import User
from app.responses.base import APIResponse, APIException
from app.logger import logger
from app.utils.globals import create_info_logging_message

router = APIRouter()

@router.post("/streaming-token")
async def create_streaming_token(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Security(deps.get_current_user, scopes=["course:create"]),  # Removed scopes parameter
) -> JSONResponse:
    """Create HeyGen streaming token - Instructor only endpoint
    
    Args:
        db (Session): Database session
        current_user (User): Current authenticated user
        
    Returns:
        JSONResponse: Streaming token or error message
    """
    try:
        # Check if user is an instructor
        if current_user.role_id != 3:
            raise APIException(
                status_code=status.HTTP_403_FORBIDDEN,
                message={
                    "success": False,
                    "message": "Only instructors can access this endpoint"
                }
            )

        # Verify API key exists
        if not settings.HEYGEN_API_KEY:
            raise APIException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                message={
                    "success": False,
                    "message": "Only instructors can access this endpoint"
                }
            )

        # Make request to HeyGen API
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{settings.HEYGEN_BASE_URL}/v1/streaming.create_token",
                headers={"x-api-key": settings.HEYGEN_API_KEY}
            )
            
            data = response.json()
            
            logger.info(
                create_info_logging_message(
                    endpoint="/streaming-token",
                    feature="heygen",
                    message="Successfully retrieved streaming token for instructor " + str(data)
                )
            )
            
            return JSONResponse(
                status_code=200,
                content=APIResponse(
                    success=True,
                    message="Streaming token retrieved successfully",
                    data={"token": data["data"]["token"]}
                )
            )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message={
                    "success": False,
                    "message": "Only instructors can access this endpoint"
                }
        )
