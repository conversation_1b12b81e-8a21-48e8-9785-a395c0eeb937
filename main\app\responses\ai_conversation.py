from pydantic import BaseModel


class ConversationResponsesClass(BaseModel):

    CREATE_SUCCESS: dict = {
        "success": True,
        "message": "Conversation created successfully.",
    }

    CREATE_ACTIVITY_EXISTS: dict = {
        "success": False,
        "message": "Conversation already exists.",
    }

    CREATE_FAILED: dict = {
        "success": False,
        "message": "An error has occured.",
    }

    UPDATE_FAILED: dict = {
        "success": False,
        "message": "Failed to update.",
    }

    ACTIVITY_NOT_EXIST: dict = {
        "success": True,
        "message": "No activity found.",
    }

    ACTIVITY_DELETED_SUCCESS: dict = {
        "success": True,
        "message": "Conversation deleted successfully.",
    }
    
    ACTIVITY_UPDATED: dict = {
        "success": True,
        "message": "Conversation updated successfully.",
    }

    DELETE_FAILED: dict = {
        "success": False,
        "message": "Failed to delete activity.",
    }

    INVALID_CONVERSATION_ID: dict = {
        "success": False,
        "message": "Invalid conversation ID.",
    }

ConversationResponses = ConversationResponsesClass()
