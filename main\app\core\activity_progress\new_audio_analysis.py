import os
import logging
from openai import OpenAI
from dotenv import load_dotenv
import subprocess
from .constants_all_modalities import base_prompt_food_complaint
from .constants_all_modalities import base_prompt_tiger_enclosure
from .constants_all_modalities import base_prompt_panda_closure
import json
import gc
import time
import tempfile
from pydub import AudioSegment
from copy import deepcopy
import asyncio
from contextlib import contextmanager

# Configure logging
logger = logging.getLogger(__name__)

# Load the .env file
load_dotenv()

# Get the API key
api_key = os.getenv('OUR_OPENAI_API_KEY')

# Initialize OpenAI client once
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY", api_key))

# Response structure definitions
response_struct_food_complaint = json.dumps({
    "rubricEvaluation": {
        "empathyAndApology": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "constructiveResponse": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "toneAndOwnership": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "opportunityCreation": {
            "score": "integer (1-5)",
            "explanation": "string"
        }
    },
    "totalScore": "integer (0-20)",
    "suggestionsForImprovement": "string"
})

response_struct_tiger_enclosure = json.dumps({
    "rubricEvaluation": {
        "respectfulBoundarySetting": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "diplomaticLanguage": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "explainsTheWhy": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "confidenceAndAuthority": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "redirectionOrAlternativeOffered": {
            "score": "integer (1-5)",
            "explanation": "string"
        }
    },
    "totalScore": "integer (0-25)",
    "suggestionsForImprovement": "string"
})

response_struct_panda_closure = json.dumps({
    "rubricEvaluation": {
        "acknowledgementAndApology": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "transparencyAndExplanation": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "loyaltyRecognition": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "effortToRepairTrust": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "emotionalManagement": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "visionOfCare": {
            "score": "integer (1-5)",
            "explanation": "string"
        }
    },
    "totalScore": "integer (0-30)",
    "suggestionsForImprovement": "string"
})

# Cache for response structures to avoid repeated JSON serialization
RESPONSE_STRUCT_CACHE = {
    "food_complaint": response_struct_food_complaint,
    "tiger_enclosure": response_struct_tiger_enclosure,
    "panda_closure": response_struct_panda_closure
}

# Cache for prompt templates to avoid repeated deepcopy operations
PROMPT_CACHE = {}

@contextmanager
def temp_file(suffix=None):
    """Context manager for temporary files that ensures cleanup."""
    temp = tempfile.NamedTemporaryFile(suffix=suffix, delete=False)
    try:
        yield temp.name
    finally:
        try:
            os.remove(temp.name)
        except OSError as e:
            logger.error(f"Error removing temporary file {temp.name}: {e}")

def extract_audio_from_video(video_path):
    """Extract audio from video using ffmpeg with proper error handling."""
    try:
        # Create a temporary file path for the audio output
        audio_output_path = tempfile.mktemp(suffix=".wav")
        
        # Run ffmpeg command to extract audio
        command = f"ffmpeg -i \"{video_path}\" -q:a 0 -map a \"{audio_output_path}\" -y"
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"FFmpeg error: {result.stderr}")
            raise RuntimeError(f"Failed to extract audio: {result.stderr}")
            
        # Verify the file exists after extraction
        if not os.path.exists(audio_output_path):
            raise FileNotFoundError(f"Audio file not created at {audio_output_path}")
            
        logger.info(f"Audio extracted to {audio_output_path}")
        
        # Read the audio data into memory before the file is deleted
        audio_data = open(audio_output_path, "rb").read()
        
        # Create a new temporary file that will persist
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_file.write(audio_data)
            persistent_path = temp_file.name
            
        logger.info(f"Audio saved to persistent path: {persistent_path}")
        return persistent_path
    except Exception as e:
        logger.exception(f"Error extracting audio: {str(e)}")
        raise

def transcribe_audio(input_audio_path):
    """Transcribe audio using OpenAI's Whisper API with error handling."""
    try:
        with open(input_audio_path, "rb") as audio_file:
            transcription = client.audio.transcriptions.create(
                model="whisper-1",
                language="en",
                file=audio_file,
                response_format="json"
            )
        return transcription.text
    except Exception as e:
        logger.exception(f"Error transcribing audio: {str(e)}")
        raise

def is_audio_empty(audio_path, silence_threshold=-50.0):
    """Check if audio file contains meaningful sound."""
    try:
        audio = AudioSegment.from_wav(audio_path)
        loudness = audio.dBFS
        return loudness < silence_threshold
    except Exception as e:
        logger.exception(f"Error checking audio: {str(e)}")
        raise

def get_prompt_template(video_type):
    """Get cached prompt template to avoid repeated deepcopy operations."""
    if video_type not in PROMPT_CACHE:
        if video_type == "food_complaint":
            PROMPT_CACHE[video_type] = base_prompt_food_complaint
        elif video_type == "tiger_enclosure":
            PROMPT_CACHE[video_type] = base_prompt_tiger_enclosure
        elif video_type == "panda_closure":
            PROMPT_CACHE[video_type] = base_prompt_panda_closure
        else:
            raise ValueError(f"Invalid video type: {video_type}")
    return deepcopy(PROMPT_CACHE[video_type])

def process_single_video(transcription_text, video_type="food_complaint"):
    """Process video with OpenAI API, optimized for memory and token usage."""
    try:
        # Get prompt template from cache
        prompt = get_prompt_template(video_type)
        
        # Get response structure from cache
        if video_type not in RESPONSE_STRUCT_CACHE:
            raise ValueError(f"Invalid video type: {video_type}")
        response_struct = RESPONSE_STRUCT_CACHE[video_type]

        # Add response format instruction
        prompt[-1]['content'].append({
            "type": "text",
            "text": f"Please return response in json of format {response_struct}. Now, Here are the video frames, audio spectrogram and transcription:\n"
        })
        
        # Add transcription
        prompt[-1]['content'].append({
            "type": "text",
            "text": f"Transcription: \"{transcription_text}\"\n"
        })
        
        # API call parameters
        params = {
            "model": "gpt-4o",
            "response_format": {"type": "json_object"},
            "messages": prompt,
            "max_tokens": 3000,
        }
        
        # Make API call
        result = client.chat.completions.create(**params)
        output_text = result.choices[0].message.content
        
        # Clean up
        del prompt
        gc.collect()
        
        return output_text
    except Exception as e:
        logger.exception(f"Error processing video: {str(e)}")
        raise

async def process_single_video_async(transcription_text, video_type="food_complaint"):
    """Async version of process_single_video for concurrent processing."""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(
        None, 
        process_single_video, 
        transcription_text, 
        video_type
    )

def new_analyze_audio_from_video(video_path, video_type="food_complaint"):
    """Main function to analyze audio from video with improved error handling and memory management."""
    start_time = time.time()
    audio_path = None
    
    try:
        logger.info(f"Starting analysis of video: {video_path} (type: {video_type})")
        
        # Extract audio
        audio_path = extract_audio_from_video(video_path)
        
        # Check if audio is empty
        if is_audio_empty(audio_path):
            logger.warning("The extracted audio is empty or below threshold")
            raise ValueError("The extracted audio is empty or contains no meaningful sound.")
        
        logger.info("Audio contains sound, continuing with analysis")
        
        # Transcribe the audio
        transcription_text = transcribe_audio(audio_path)
        
        # Process video
        output_text = process_single_video(transcription_text, video_type)
        output = json.loads(output_text)
        
        end_time = time.time()
        logger.info(f"Processing completed in {end_time - start_time:.2f} seconds")
        
        return output
        
    except Exception as e:
        logger.exception(f"Error in new_analyze_audio_from_video: {str(e)}")
        raise
        
    finally:
        # Clean up resources
        try:
            if audio_path and os.path.exists(audio_path):
                os.remove(audio_path)
                
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")
            
        # Force garbage collection
        collected = gc.collect()
        logger.debug(f"Garbage collector: collected {collected} objects.")

async def new_analyze_audio_from_video_async(video_path, video_type="food_complaint"):
    """Async version of the main analysis function."""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, new_analyze_audio_from_video, video_path, video_type)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    video_path = "Faisal-average-response.mp4"
    output = new_analyze_audio_from_video(video_path)
    print(output)

