"""
This file contains all the general responses 
"""

from pydantic import BaseModel


class GeneralResponsesClass(BaseModel):
    GENERAL_DB_EXCEPTION: dict = {
        "success": False,
        "message": "Database exception.",
    }

    GENERAL_EXCEPTION: dict = {
        "success": False,
        "message": "We are unable to process your request at this time.",
    }

    DATA_DOES_NOT_EXITS: dict = {
        "success": False,
        "message": "Data doesn't exits.",
    }

    NO_ACCESS: dict = {
        "success": False,
        "message": "You do not have access to this resource.",
    }


GeneralResponses = GeneralResponsesClass()
