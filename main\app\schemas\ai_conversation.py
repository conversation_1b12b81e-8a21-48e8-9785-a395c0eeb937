from datetime import datetime

from pydantic import BaseModel
from typing import List


class ConversationCreate(BaseModel):
    conversation_id: int | None = None
    course_id: int | None = None
    activity_id: int | None = None
    screen: str | None = None
    message: str


class ConversationUpdate(ConversationCreate):
    ...


class ConversationInDB(BaseModel):
    user_id: int
    course_id: int | None = None
    activity_id: int | None = None
    screen: str | None = None
    prompts_count: int = 0
    
    
class MessageCreate(BaseModel):
    conversation_id: int | None = None
    message: str | None = None
    sender: str | None = None
    
    
class MessageUpdate(MessageCreate):
    ...
    
    
class MessageInDB(MessageCreate):
    ...
    
    
class MessageReturn(BaseModel):
    conversation_id: int | None = None
    message: str | None = None
    sender: str | None = None
    created_at: datetime
    # updated_at: datetime
    
    class Config:
        orm_mode = True
        

class ConversationReturn(BaseModel):
    id: int | None = None
    user_id: int
    course_id: int | None = None
    activity_id: int | None = None
    screen: str | None = None
    chatbot_response: MessageReturn
    total_user_prompts: int | None = None
    # messages: List[MessageReturn] = []
    
    class Config:
        orm_mode = True
        
        
        
        
        
        