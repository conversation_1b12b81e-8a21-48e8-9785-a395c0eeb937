# API Responses

A collection of pre-defined API response objects.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)
2. [Example](#example)

## Description

_NOTE: These are **NOT** http response objects._

API responses are pre-defined concrete objects that should be sent to the client for different API states (e.g. success, failure, entity created, entity updated, entity deleted, etc.). They provide context to the client regarding what happened in the backend. Information in these objects will be used to create the actual http response.

This is to address the following concerns:

- If the API succeeds or fails, what exactly happened?
- How can the client communicate the error or success with the backend team in regards to the context of the application state?

The list of all responses are available in the [backend response codes sheet](https://docs.google.com/spreadsheets/d/1g3dxBJjWH084q_7oj_qzVdim2UdTBwJWeyBYQIZli9U/edit?usp=sharing).

## Usage

An API endpoint should have a corresponding response file, containing all response objects for different states. A response object is in the following format:

```
<name of state>: dict = {
    "success": <boolean of whether to return a success of error to the client>,
    "message": <string of the message to send to the client>,
}
```
## Example

This is an example taken from `user.py`.

Assuming we want to create an endpoint that creates a new `User`. Before even thinking about the logic, we can start defining what the response code and message should be when the operation is successful. The response can look something like this:

```python
# responses/user.py

from pydantic import BaseModel


class UserResponsesClass(BaseModel):
    USER_CREATE_SUCCESS: dict = {
        "success": True,
        "message": "User created successfully.",
    }
```

Then import it to the `__init__.py` to help the rest of the application import from `schemas`:

```python
# responses/__init__.py

from .user import UserResponsesClass
```

We can use this to create an http response object in the endpoint code. In the code below, we send both `USER_CREATE_SUCCESS` and `user_out` as responses, with `user_out` being in the `data`:

```python
# endpoints/user.py

from fastapi import Depends
from starlette.responses import JSONResponse

from app import models, schemas
from app.responses import UserResponsesClass
from app.responses.base import APIResponse


@router.post("/invite")
def user_invite(*, db: Session = Depends(deps.get_db), user_in: schemas.UserInvite) -> JSONResponse:

    ...

    user_out = <some operation>

    # Return the response object plus user_out data
    return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponsesClass.USER_CREATE_SUCCESS,
                data={"user": user_out},
            ),
        )
```

The client would receive a response that looks like this:

```json
{
    "success": true,
    "message": "User created successfully.",
    "data": {
        "user": <user_out data>
    }
}
```
