"""
This file contains all the crud operations for course
"""

from typing import Optional

from app.crud.base import CRUDBase
from app.models.course import Course
from app.schemas.course import CourseCreate, CourseUpdate

from sqlalchemy.orm import Session


class CRUDCourse(CRUDBase[Course, CourseCreate, CourseUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Course]:
        return db.query(Course).filter(Course.name == name).first()

    def get_by_id(self, db: Session, *, id: int) -> Optional[Course]:
        return db.query(Course).filter(Course.id == id).first()

    def get_all_permissions(self, db: Session) -> list[Course]:
        return db.query(Course).all()


course = CRUDCourse(Course)
