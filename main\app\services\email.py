import smtplib
from smtplib import *
from email.mime.text import MIMEText
from email.utils import formataddr


class EmailClient(object):
    # todo we should add comments
    def __init__(
        self,
        EMAIL_FROM_EMAIL: str,
        EMAIL_FROM_NAME: str,
        EMAIL_SMTP_TLS: bool,
        E<PERSON>IL_SMTP_SSL: bool,
        EMAIL_SMTP_PORT: int,
        EMAIL_SMTP_HOST: str,
        EMAIL_SMTP_USERNAME: str,
        EMAIL_SMTP_PASSWORD: str,
    ):

        self.EMAIL_FROM_EMAIL = EMAIL_FROM_EMAIL
        self.EMAIL_FROM_NAME = EMAIL_FROM_NAME
        self.SMTP_TLS = EMAIL_SMTP_TLS
        self.SMTP_SSL = EMAIL_SMTP_SSL
        self.SMTP_PORT = EMAIL_SMTP_PORT
        self.SMTP_HOST = EMAIL_SMTP_HOST
        self.SMTP_USERNAME = EMAIL_SMTP_USERNAME
        self.SMTP_PASSWORD = EMAIL_SMTP_PASSWORD

    def send_email(self, to_email: str, subject: str, body: str):
        """Send an email to the user.

        Args:
            to_email (str): The email address to send the email to.
            subject (str): The subject of the email.
            body (str): The body of the email.
        """
        msg = MIMEText(body)
        msg["Subject"] = subject
        msg["From"] = formataddr((self.EMAIL_FROM_NAME, self.EMAIL_FROM_EMAIL))
        msg["To"] = to_email

        self.smtp = smtplib.SMTP(self.SMTP_HOST, self.SMTP_PORT)
        # identify ourselves to smtp gmail client
        self.smtp.ehlo()
        # secure our email with tls encryption
        self.smtp.starttls()
        # re-identify ourselves as an encrypted connection
        self.smtp.ehlo()
        self.smtp.login(self.SMTP_USERNAME, self.SMTP_PASSWORD)

        self.smtp.sendmail(self.SMTP_USERNAME, to_email, msg.as_string())
        self.smtp.close()
