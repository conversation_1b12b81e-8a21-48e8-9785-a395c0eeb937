"""
This file contains the model mapping of Activity
"""

from datetime import datetime, timezone

from app.db.base_class import Base
# from app.models.association_tables import course_activity_association
from sqlalchemy.orm import relationship

from sqlalchemy import (
    Column,
    DateTime,
    String,
    Integer,
    ForeignKey
)
    

class Conversation(Base):
    user_id = Column(Integer, ForeignKey('user.id'))
    course_id = Column(Integer, ForeignKey('course.id'))
    activity_id = Column(Integer, ForeignKey('activity.id'))
    prompts_count = Column(Integer)
    screen = Column(String(40), comment='Screen name from where conversation is started')
    messages = relationship('Message', backref='conversation', lazy='joined')
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                        onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    
    
class Message(Base):
    conversation_id = <PERSON>umn(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>('conversation.id'), default=None)
    message = Column(String, comment="query from user")
    sender = Column(String(40), comment="sender (user or chatbot)")
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                        onupdate=lambda: datetime.now(timezone.utc), nullable=False)