import psutil
import time


def memory_usage(func):
    def inner(*args, **kwargs):
        start_time = time.time()
        start_memory = round((psutil.virtual_memory()[3]/1000000000), 4)
        f = func(*args, **kwargs)
        end_memory = round((psutil.virtual_memory()[3]/1000000000), 4)
        end_time = time.time()
        print(f"{func.__name__} memory: Start: {start_memory} End: {end_memory} Increase: {end_memory - start_memory}"
              f" Time Consumed: {end_time - start_time}")
        return f
    return inner