from pydantic import BaseModel


class AuthResponsesClass(BaseModel):
    AUTH_LOGIN_SUCCESS: dict = {
        "success": True,
        "message": "logged in successfully.",
    }

    AUTH_REFRESH_SUCCESS: dict = {
        "success": True,
        "message": "refreshed token successfully.",
    }

    AUTH_LOGIN_INVALID_CREDENTIALS: dict = {
        "success": False,
        "message": "invalid credentials, recheck password and email.",
    }

    AUTH_REFRESH_TOKEN_INVALID: dict = {
        "success": False,
        "message": "invalid refresh token.",
    }

    AUTH_SIGNUP_USER_EXISTS: dict = {
        "success": False,
        "message": "user already exists.",
    }

    AUTH_SIGNUP_USER_NOT_FOUND: dict = {
        "success": False,
        "message": "user not found.",
    }

    PASSWORD_CHANGED_SUCCESS: dict = {
        "success": True,
        "message": "Password changed successfully.",
    }

    PASSWORD_INVALID: dict = {
        "success": False,
        "message": "Old password is incorrect.",
    }


AuthResponses = AuthResponsesClass()
