[project]
name = "Backend"
version = "0.1.0"
description = "New Migrations of IAL-Skillseed"
authors = [{ name = "<PERSON><PERSON>", email = "<EMAIL>" }]
requires-python = "~=3.10"
readme = "README.md"
dependencies = [
    "fastapi==0.87.0",
    "SQLAlchemy-Utils>=0.38.3,<0.39",
    "SQLAlchemy>=1.4.41,<2",
    "pydantic[email]>=1.10.2,<2",
    "passlib>=1.7.4,<2",
    "uvicorn[standard]>=0.18.3,<0.19",
    "python-jose[cryptography]>=3.3.0,<4",
    "python-multipart>=0.0.5,<0.0.6",
    "fastapi-route-logger-middleware>=0.2.3,<0.3",
    "StrEnum>=0.4.8,<0.5",
    "alembic>=1.11.1,<2",
    "tenacity>=8.1.0,<9",
    "gunicorn>=21.1.0,<22",
    "pyproj==3.4.0",
    "fastapi-pagination[all]>=0.10.0,<0.11",
    "pycountry>=22.3.5,<23",
    "oauthlib>=3.2.2,<4",
    "requests-oauthlib>=1.3.1,<2",
    "fastapi-analytics>=1.1.4,<2",
    "bcrypt>=4.0.1,<5",
    "replicate==0.18.1",
    "psycopg2-binary",
    "numpy==1.26.4",
    "opencv-python>=*********,<5",
    "librosa==0.10.2",
    "openai==1.55.3",
    "noisereduce==3.0.2",
    "python-docx==1.1.2",
    "boto3>=1.34.146,<2",
    "pydub>=0.25.1,<0.26",
    "pymupdf>=1.24.10,<2",
]

[dependency-groups]
dev = [
    "isort>=5.10.1,<6",
    "black>=22.8.0,<23",
    "ipykernel>=6.16.0,<7",
    "matplotlib>=3.6.0,<4",
    "pandas>=1.5.0,<2",
    "XlsxWriter>=3.0.3,<4",
    "openpyxl>=3.0.10,<4",
    "yaspin>=2.2.0,<3",
    "aiohttp>=3.8.3,<4",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app/main.py"]