"""
This file contains all the API's related to simulation scripts generation
and tutorial script generation
"""

import json
from fastapi import APIRouter, Security, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from app.api import deps
from app.models import User
from app.responses.base import APIResponse, APIException
from app.logger import logger
from app.utils.globals import create_info_logging_message
from app.core.config import settings
from openai import OpenAI
from pydantic import BaseModel

router = APIRouter()

class SimulationScriptRequest(BaseModel):
    industry: str
    simulation_objective: str
    role: str
    learning_outcome: str

class TutorialScriptRequest(BaseModel):
    learning_objectives: str
    learner_profile: str

@router.post("/generate-scripts")
async def generate_simulation_scripts(
    *,
    request_data: SimulationScriptRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Security(deps.get_current_user, scopes=["course:create"]),
) -> JSONResponse:
    """Generate simulation scripts - Instructor only endpoint

    Args:
        request_data (SimulationScriptRequest): Input parameters for script generation
        db (Session): Database session
        current_user (User): Current authenticated user

    Returns:
        JSONResponse: Generated scripts or error message
    """
    try:
        # Check if user is an instructor
        if current_user.role_id != 3:
            raise APIException(
                status_code=status.HTTP_403_FORBIDDEN,
                message={
                    "success": False,
                    "message": "Only instructors can access this endpoint"
                }
            )

        # Construct the prompt
        json_struct = {"scripts": ["script text", "script text", "script text"]}
        prompt = f"""
        You are an expert in writing short training simulations for role-based learning.

        Generate 3 one-way customer interaction scripts (30-60 seconds each), designed for the following setup:

        - Industry: {request_data.industry}
        - Simulation Objective: {request_data.simulation_objective}
        - Role the Simulation is for: {request_data.role}
        - Expected Learning Outcome: {request_data.learning_outcome}

        Each script should include:
        - A realistic and emotional customer dialogue (as if speaking to the associate)
        - Do NOT include any response from the sales associate
        - only include the customer's dialogue

        Please return response in json of format {json_struct} just return the script text don't write script 1: 'script text ...'
        """

        # Initialize OpenAI client with API key from settings
        client = OpenAI(api_key=settings.openai.OUR_OPENAI_API_KEY)

        # Generate scripts
        response = client.chat.completions.create(
            model="gpt-4o",  # Changed from gpt-4o to gpt-4 as it's the correct model name
            messages=[
                {"role": "system", "content": "You create realistic one-way roleplay scripts for simulation training."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"},
            temperature=0.0,
            max_tokens=1000
        )

        scripts_output = response.choices[0].message.content

        logger.info(
            create_info_logging_message(
                endpoint="/generate-scripts",
                feature="simulation",
                message="Successfully generated simulation scripts" + str(scripts_output)
            )
        )

        return JSONResponse(
            status_code=200,
            content=APIResponse(
                success=True,
                message="Simulation scripts generated successfully",
                data=json.loads(scripts_output)
            )
        )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message={
                "success": False,
                "message": "Failed to generate simulation scripts"
            }
        )

@router.post("/generate-tutorial")
async def generate_tutorial_script(
    *,
    request_data: TutorialScriptRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Security(deps.get_current_user, scopes=["course:create"]),
) -> JSONResponse:
    """Generate tutorial script - Instructor only endpoint

    Args:
        request_data (TutorialScriptRequest): Input parameters for tutorial script generation
        db (Session): Database session
        current_user (User): Current authenticated user

    Returns:
        JSONResponse: Generated tutorial script or error message
    """
    try:
        # Check if user is an instructor
        if current_user.role_id != 3:
            raise APIException(
                status_code=status.HTTP_403_FORBIDDEN,
                message={
                    "success": False,
                    "message": "Only instructors can access this endpoint"
                }
            )

        json_struct_tutorial = {"tutorial_script": "script text"} # Define the expected JSON structure for the tutorial

        prompt = f"""
        You are an expert educational content designer and scriptwriter for short, engaging tutorial videos. Create a monologue script of approximately 130 words that helps the learner achieve the following:

        Learning Objectives: {request_data.learning_objectives}

        Learner Profile: {request_data.learner_profile}

        Use clear, accessible language. Start with a brief hook, move into an explanation or demonstration, and finish with a quick summary and call to action. Define any essential terms. Write for a one-minute voiceover pacing and end with a simple, action-oriented closing statement that reinforces learning. Do not include any headings, labels, or section titles.
        Please provide the output exclusively as a JSON object with a single key, 'tutorial_script', containing the generated script text as its value. The expected format is {json_struct_tutorial}. Only return the JSON object.
        """

        # Initialize OpenAI client with API key from settings
        client = OpenAI(api_key=settings.openai.OUR_OPENAI_API_KEY)

        # Generate tutorial script
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You generate concise tutorial scripts for videos."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"},
            temperature=0.0,
            max_tokens=500
        )

        tutorial_output = response.choices[0].message.content

        logger.info(
            create_info_logging_message(
                endpoint="/generate-tutorial",
                feature="simulation",
                message="Successfully generated tutorial script" + str(tutorial_output)
            )
        )

        return JSONResponse(
            status_code=200,
            content=APIResponse(
                success=True,
                message="Tutorial script generated successfully",
                data=json.loads(tutorial_output)
            )
        )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message={
                "success": False,
                "message": "Failed to generate tutorial script"
            }
        )
