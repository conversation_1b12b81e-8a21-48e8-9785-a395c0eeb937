"""
Script to add public_url column to the heygen_video table
"""

import os
import sys
from sqlalchemy import create_engine, Column, String, MetaData, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection
DATABASE_URL = os.getenv('DATABASE_CONNECTION_URL')
if not DATABASE_URL:
    print("Error: DATABASE_CONNECTION_URL environment variable not set")
    sys.exit(1)

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create metadata object
metadata = MetaData()

def add_public_url_column():
    """Add public_url column to heygen_video table"""
    try:
        # Connect to the database
        connection = engine.connect()
        
        # Check if the heygen_video table exists
        inspector = inspect(engine)
        if 'heygen_video' not in inspector.get_table_names():
            print("Error: heygen_video table does not exist")
            return
        
        # Check if the public_url column already exists
        columns = [col['name'] for col in inspector.get_columns('heygen_video')]
        if 'public_url' in columns:
            print("public_url column already exists in heygen_video table")
            return
        
        # Add the public_url column
        connection.execute(
            'ALTER TABLE heygen_video ADD COLUMN public_url VARCHAR;'
        )
        
        print("Successfully added public_url column to heygen_video table")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        connection.close()

if __name__ == "__main__":
    # Import inspect here to avoid circular imports
    from sqlalchemy import inspect
    add_public_url_column()
