"""
This file contains all the Schemas 
of Role & Permissions feature
"""

from pydantic import BaseModel
from datetime import datetime


class RolePermissionBase(BaseModel):
    role_id: int
    permission_id: int


class RolePermissionCreate(RolePermissionBase):
    ...


class RolePermissionUpdate(RolePermissionBase):
    ...



class RolePermissionInDBBase(RolePermissionBase):
    id: int | None = None
    created_on: datetime | None
    last_updated: datetime | None

    class Config:
        orm_mode = True


class RolePermission(RolePermissionInDBBase):
    ...


class RolePermissionListOut(BaseModel):
    id: int
    role: str
    permission: str
    created_on: datetime
    last_updated: datetime
    

