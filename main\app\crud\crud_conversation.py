"""
This file contains all the crud operations for ai conversations
"""

from typing import Optional, List

from app.crud.base import CRUDBase
from app.models import Conversation, Message
from app.schemas import ConversationCreate, ConversationUpdate, MessageCreate, MessageUpdate

from sqlalchemy.orm import Session
from sqlalchemy.sql.functions import sum


class CRUDConversation(CRUDBase[Conversation, ConversationCreate, ConversationUpdate]):
    def update_prompts_count(self, db: Session, *, conversation_id: int):
        db.query(Conversation).filter(Conversation.id == conversation_id).update({'prompts_count': Conversation.prompts_count + 1})
        
    def get_conversation_count_by_user(self, db: Session, *, user_id: int) -> Optional[Conversation]:
        return db.query(sum(Conversation.prompts_count)).filter(Conversation.user_id == user_id).scalar()
    
    def get_by_user(self, db: Session, *, user_id: int) -> Optional[Conversation]:
        return db.query(Conversation).filter(Conversation.user_id == user_id).all()

    def get_by_id(self, db: Session, *, id: int) -> Optional[Conversation]:
        return db.query(Conversation).filter(Conversation.id == id).first()

    def get_all_conversations(self, db: Session) -> List[Conversation]:
        return db.query(Conversation).all()
    
    
class CRUDMessage(CRUDBase[Message, MessageCreate, MessageUpdate]):
    def get_by_conversation_id(self, db: Session, *, conversation_id: int) -> List[Message]:
        return db.query(Message).filter(Message.conversation_id == conversation_id).all()


conversation = CRUDConversation(Conversation)
message = CRUDMessage(Message)
