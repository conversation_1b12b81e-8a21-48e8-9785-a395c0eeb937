from pydantic import BaseModel


class JWTResponsesClass(BaseModel):
    INVALID_CREDENTIALS: dict = {
        "success": False,
        "message": "invalid credentials, recheck password and email.",
    }

    JWT_TOKEN_INVALID: dict = {
        "success": False,
        "message": "authorization token expired or invalid.",
    }

    NOT_ENOUGH_PERMISSIONS: dict = {
        "success": False,
        "message": "no permission to perform this action.",
    }


JWTResponses = JWTResponsesClass()
