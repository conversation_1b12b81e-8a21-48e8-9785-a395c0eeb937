"""
This file contains all the CRUD operations
for Organization model
"""

from typing import List, Optional, Dict, Any, Union

from app.models.organization import Organization, OrganizationStatus
from app.schemas.organization import OrganizationCreate, OrganizationUpdate

from sqlalchemy.orm import Session


def get(db: Session, id: int) -> Optional[Organization]:
    """Get organization by ID

    Args:
        db (Session): SQLAlchemy Session
        id (int): Organization ID

    Returns:
        Optional[Organization]: Organization if found, None otherwise
    """
    return db.query(Organization).filter(Organization.id == id).first()


def get_by_name(db: Session, name: str) -> Optional[Organization]:
    """Get organization by name

    Args:
        db (Session): SQLAlchemy Session
        name (str): Organization name

    Returns:
        Optional[Organization]: Organization if found, None otherwise
    """
    return db.query(Organization).filter(Organization.name == name).first()


def get_multi(
    db: Session, *, skip: int = 0, limit: int = 100, status: Optional[OrganizationStatus] = None
) -> List[Organization]:
    """Get multiple organizations with optional status filter

    Args:
        db (Session): SQLAlchemy Session
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 100.
        status (OrganizationStatus, optional): Filter by status. Defaults to None.

    Returns:
        List[Organization]: List of organizations
    """
    query = db.query(Organization)
    if status:
        query = query.filter(Organization.status == status)
    return query.offset(skip).limit(limit).all()


def create(db: Session, *, obj_in: OrganizationCreate) -> Organization:
    """Create a new organization

    Args:
        db (Session): SQLAlchemy Session
        obj_in (OrganizationCreate): Organization data

    Returns:
        Organization: Created organization
    """
    db_obj = Organization(
        name=obj_in.name,
        description=obj_in.description,
        address=obj_in.address,
        contact_email=obj_in.contact_email,
        contact_phone=obj_in.contact_phone,
        status=OrganizationStatus.PENDING,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: Organization, obj_in: Union[OrganizationUpdate, Dict[str, Any]]
) -> Organization:
    """Update organization

    Args:
        db (Session): SQLAlchemy Session
        db_obj (Organization): Organization to update
        obj_in (Union[OrganizationUpdate, Dict[str, Any]]): Update data

    Returns:
        Organization: Updated organization
    """
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.dict(exclude_unset=True)
    
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update_status(db: Session, *, db_obj: Organization, status: OrganizationStatus) -> Organization:
    """Update organization status

    Args:
        db (Session): SQLAlchemy Session
        db_obj (Organization): Organization to update
        status (OrganizationStatus): New status

    Returns:
        Organization: Updated organization
    """
    db_obj.status = status
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def remove(db: Session, *, id: int) -> Organization:
    """Remove organization

    Args:
        db (Session): SQLAlchemy Session
        id (int): Organization ID

    Returns:
        Organization: Removed organization
    """
    obj = db.query(Organization).get(id)
    db.delete(obj)
    db.commit()
    return obj
