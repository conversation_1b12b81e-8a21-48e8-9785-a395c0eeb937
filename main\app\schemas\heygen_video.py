"""
This file contains all the Schemas
of HeygenVideo related feature
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class HeygenVideoBase(BaseModel):
    """Base schema for Heygen video"""
    name: str


class HeygenVideoCreate(HeygenVideoBase):
    """Schema for creating a new Heygen video"""
    pass


class HeygenVideoUpdate(BaseModel):
    """Schema for updating a Heygen video"""
    name: Optional[str] = None


class HeygenVideoInDBBase(HeygenVideoBase):
    """Base schema for Heygen video in DB"""
    id: Optional[int] = None
    s3_path: str
    public_url: Optional[str] = None
    instructor_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class HeygenVideo(HeygenVideoInDBBase):
    """Schema for returning Heygen video data"""
    pass


class HeygenVideoWithUrl(HeygenVideo):
    """Schema for returning Heygen video with presigned URL"""
    video_url: str
