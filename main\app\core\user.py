from app.utils.globals import invitation_link
from app.services import EmailClient
from app.models import User, Cohort
from app.crud import role
from app.logger import logger
from app.utils.globals import create_info_logging_message

from sqlalchemy.orm import Session
import pandas as pd
import numpy as np
import re
import io
import string
import random
import hashlib
import json


def check_user_role(db: Session, current_user: User, role_type: str):
    """
    Check role type of the logged in user
    
    Args:
        current_user: User class instance of logged in user
        role_type: role type to check
        
    Returns:
        bool: True if role_type is same as role name of logged in user
    """
    try:
        db_role = role.get_by_id(db=db, id=current_user.role_id)
        return db_role.name == role_type
    except Exception as e:
        return False
    
    
def check_file_type_and_content(file_content, file):
    cohort_name = None
    if file_content != "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        logger.info(
                create_info_logging_message(
                    endpoint="/create-multiple-users",
                    feature="user",
                    message="Upload Users File type is not Excel"),
                )
        return {}, cohort_name
    
    try:
        # contents = users_file.read()
        # df = pd.read_csv(io.BytesIO(file))
        df = pd.read_excel(io.BytesIO(file), header=None)
        cohort_row_col = np.where(df == 'Name of Cohort')

        # Extract the cohort name from the next column
        if bool(cohort_row_col[0]):
            cohort_row_col = np.where(df == 'Name of Cohort')
            cohort_row, cohort_col = cohort_row_col[0][0], cohort_row_col[1][0]
            cohort_name = df.iloc[cohort_row, cohort_col + 1]
            print(f"Cohort Name: {cohort_name}")
        else:
            print("Cohort name not found.")
            
        first_name_row = df[df.isin(['First Name']).any(axis=1)].index[0]
        df = pd.read_excel(io.BytesIO(file), skiprows=first_name_row)
    except Exception as e:
        logger.info(
                create_info_logging_message(
                    endpoint="/create-multiple-users",
                    feature="user",
                    message=str(f"Error reading the file: {e}"),
                )
            )
        return {}, cohort_name

    # Check if the required headers are present
    required_headers = {"First Name", "Email address"}
    if not required_headers.issubset(df.columns):
        missing_headers = required_headers - set(df.columns)
        logger.info(
                create_info_logging_message(
                    endpoint="/create-multiple-users",
                    feature="user",
                    message=str(f"Missing required headers: {', '.join(missing_headers)}"),
                )
            )
        return {}, cohort_name
    df['Date of Birth'] = pd.to_datetime(df['Date of Birth'], format='%Y%m%d')
    
    return json.loads(df.to_json(orient='records')), cohort_name


def get_cohort_by_file_hash(db:Session, file_hash: str):
    return db.query(Cohort).filter(Cohort.file_hash == file_hash).first()


def calculate_file_hash(file) -> str:
    """
    Calculate the hash of the uploaded file using the specified hash algorithm.
    """
    return hashlib.md5(file).hexdigest()


def generate_password(length=12):
    """
    Function to generate random strong password for user
    """

    upper = string.ascii_uppercase
    lower = string.ascii_lowercase
    digits = string.digits
    special = string.punctuation

    # Ensure the password contains at least one character from each set
    all_characters = [
        random.choice(upper),
        random.choice(lower),
        random.choice(digits),
        random.choice(special)
    ]

    if length > 4:
        all_characters += random.choices(upper + lower + digits + special, k=length-4)

    random.shuffle(all_characters)

    password = ''.join(all_characters)
    
    return password


def is_valid_email(email: str):
    """
    Check if email string is of correct format
    """
    email_regex = re.compile(
        r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)"
    )
    
    return bool(email_regex.match(email))


def send_invitation_email(email_client: EmailClient, email: str, password: str):
    """Send email to user when user has been invited.

    Args:
        email_client: Email class instance
        email: email of the user to send email to
    """
    try:
        body=f"""You have been invited to join Mandai. \n Follow the attached link: {invitation_link} 
            \n Your email is: {email} and your password is: {password} 
            \n **Note:** Please change your password after login."""
        
        email_client.send_email(
            to_email=email,
            subject="Your invitation to join Digital Cloud",
            body=body,
        )
    except Exception as e:
        return

