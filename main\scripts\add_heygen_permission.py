
import os
import sys
from pathlib import Path

# Add the parent directory to Python path
current_dir = Path(__file__).resolve().parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

from sqlalchemy.orm import Session
from app import crud, schemas
from app.db.session import <PERSON>Local
from app.scopes.heygen import HeyGenScopes

def add_heygen_permission():
    db = SessionLocal()
    try:
        # Create the permission
        permission_in = schemas.PermissionCreate(
            name=HeyGenScopes.create_token,
            description="Permission to create HeyGen streaming tokens"
        )
        permission = crud.permission.create(db=db, obj_in=permission_in)
        
        # Get the instructor role
        instructor_role = crud.role.get_by_name(db=db, name="instructor")
        
        # Assign permission to instructor role
        role_permission_in = schemas.RolePermissionCreate(
            role_id=instructor_role.id,
            permission_id=permission.id
        )
        crud.role_permission.create(db=db, obj_in=role_permission_in)
        
        print("Successfully added HeyGen permission to instructor role")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    add_heygen_permission()