"""
<PERSON><PERSON><PERSON> to add HeygenVideo permissions to the database
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import crud, schemas
from app.db.session import SessionLocal
from app.scopes.heygen_video import HeygenVideoScopes
from app.utils.globals import AvailableRoles
from app.logger import logger


def add_heygen_video_permissions():
    db = SessionLocal()
    try:
        # Define all HeygenVideo permissions
        permissions = [
            {"name": HeygenVideoScopes.create, "description": "Permission to create Heygen videos"},
            {"name": HeygenVideoScopes.read, "description": "Permission to read Heygen video details"},
            {"name": HeygenVideoScopes.update, "description": "Permission to update Heygen video details"},
            {"name": HeygenVideoScopes.delete, "description": "Permission to delete Heygen videos"},
            {"name": HeygenVideoScopes.use_in_activity, "description": "Permission to use Heygen videos in activities"}
        ]
        
        # Create permissions and store their IDs
        permission_ids = {}
        for perm in permissions:
            # Check if permission already exists
            existing_perm = crud.permission.get_by_name(db=db, name=perm["name"])
            if existing_perm:
                logger.info(f"Permission '{perm['name']}' already exists")
                permission_ids[perm["name"]] = existing_perm.id
            else:
                # Create new permission
                permission_in = schemas.PermissionCreate(
                    name=perm["name"],
                    description=perm["description"]
                )
                new_perm = crud.permission.create(db=db, obj_in=permission_in)
                permission_ids[perm["name"]] = new_perm.id
                logger.info(f"Created permission '{perm['name']}'")
        
        # Get the instructor role
        instructor_role = crud.role.get_by_name(db=db, name=AvailableRoles.instructor)
        if not instructor_role:
            logger.error(f"Role '{AvailableRoles.instructor}' not found")
            return
        
        # Assign permissions to instructor role
        for perm_name, perm_id in permission_ids.items():
            # Check if role-permission already exists
            if not crud.role_permission.get_role_permission_by_id(
                db=db, role_id=instructor_role.id, permission_id=perm_id
            ):
                role_permission_in = schemas.RolePermissionCreate(
                    role_id=instructor_role.id,
                    permission_id=perm_id
                )
                crud.role_permission.create(db=db, obj_in=role_permission_in)
                logger.info(f"Assigned permission '{perm_name}' to role '{AvailableRoles.instructor}'")
            else:
                logger.info(f"Permission '{perm_name}' already assigned to role '{AvailableRoles.instructor}'")
        
        logger.info("HeygenVideo permissions setup completed successfully")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    add_heygen_video_permissions()
