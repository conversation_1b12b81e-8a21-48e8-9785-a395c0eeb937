"""
<PERSON><PERSON><PERSON> to add organization-related permissions to roles.
This script:
1. Creates the organization-related permissions
2. Assigns these permissions to the appropriate roles (super_admin and admin)
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.db.session import SessionLocal
from app import crud, schemas
from app.scopes import OrganizationScopes
from app.utils.globals import AvailableRoles
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_organization_permissions():
    db = SessionLocal()
    try:
        # Reset the sequence for permission table
        db.execute("SELECT setval('permission_id_seq', (SELECT MAX(id) FROM permission));")
        db.commit()
        
        # Define all organization permissions
        permissions = [
            {"name": OrganizationScopes.create, "description": "Permission to create organizations"},
            {"name": OrganizationScopes.read, "description": "Permission to read organization details"},
            {"name": OrganizationScopes.update, "description": "Permission to update organization details"},
            {"name": OrganizationScopes.delete, "description": "Permission to delete organizations"},
            {"name": OrganizationScopes.update_status, "description": "Permission to update organization status"},
            {"name": OrganizationScopes.create_admin, "description": "Permission to create organization admins"}
        ]
        
        # Create permissions if they don't exist
        permission_ids = {}
        for perm in permissions:
            existing_perm = crud.permission.get_by_name(db=db, name=perm["name"])
            if existing_perm:
                logger.info(f"Permission '{perm['name']}' already exists")
                permission_ids[perm["name"]] = existing_perm.id
                continue
                
            permission_in = schemas.PermissionCreate(
                name=perm["name"],
                description=perm["description"]
            )
            new_perm = crud.permission.create(db=db, obj_in=permission_in)
            permission_ids[perm["name"]] = new_perm.id
            logger.info(f"Created permission '{perm['name']}' with ID {new_perm.id}")
        
        # Get roles
        super_admin_role = crud.role.get_by_name(db=db, name=AvailableRoles.super_admin)
        admin_role = crud.role.get_by_name(db=db, name=AvailableRoles.admin)
        
        if not super_admin_role:
            logger.error(f"Role '{AvailableRoles.super_admin}' not found")
            return
            
        if not admin_role:
            logger.error(f"Role '{AvailableRoles.admin}' not found")
            return
        
        # Assign all permissions to super_admin
        for perm_name, perm_id in permission_ids.items():
            if not crud.role_permission.get_role_permission_by_id(
                db=db, role_id=super_admin_role.id, permission_id=perm_id
            ):
                role_permission_in = schemas.RolePermissionCreate(
                    role_id=super_admin_role.id,
                    permission_id=perm_id
                )
                crud.role_permission.create(db=db, obj_in=role_permission_in)
                logger.info(f"Assigned permission '{perm_name}' to role '{AvailableRoles.super_admin}'")
            else:
                logger.info(f"Permission '{perm_name}' already assigned to role '{AvailableRoles.super_admin}'")
        
        # Assign read permission to admin
        read_perm_id = permission_ids.get(OrganizationScopes.read)
        if read_perm_id and not crud.role_permission.get_role_permission_by_id(
            db=db, role_id=admin_role.id, permission_id=read_perm_id
        ):
            role_permission_in = schemas.RolePermissionCreate(
                role_id=admin_role.id,
                permission_id=read_perm_id
            )
            crud.role_permission.create(db=db, obj_in=role_permission_in)
            logger.info(f"Assigned permission '{OrganizationScopes.read}' to role '{AvailableRoles.admin}'")
        else:
            logger.info(f"Permission '{OrganizationScopes.read}' already assigned to role '{AvailableRoles.admin}'")
        
        logger.info("Organization permissions setup completed successfully")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error adding organization permissions: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    add_organization_permissions()

