#!/bin/bash
# cd "$(dirname "$0")"
# echo $dirname
# # set access & error logs
LOG_FILE="/var/log/IAL.log"
# connect to virtual env
# source .venv/bin/activate
# poetry install
# shellcheck disable=SC2046
# export $(grep -v '^#' /app/dev.env | xargs)
echo Starting The Gunicorn Server...
exec gunicorn app.main:app --reload --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:5000
# exec gunicorn -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:5000 app.main:app  >> "$LOG_FILE" 2>&1
exec "$@"