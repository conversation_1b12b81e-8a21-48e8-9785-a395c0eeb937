from typing import Dict
from app.models.permission import Permission
from sqlalchemy.orm import Session


def get_scopes_from_database(db: Session) -> Dict:
    """Get all scopes from database

    Args:
        db (Session): The database session.

    Returns:
        Dict: A dictionary of scopes.
    """

    permissions = db.query(Permission).all()
    # Format permissions to match OAuth2 scopes
    scopes = {
        f"{permission.name}": permission.description for permission in permissions
    }
    return scopes
