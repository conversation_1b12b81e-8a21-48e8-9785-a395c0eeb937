# API

A collection of fastapi endpoints.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)

## Description

This is the key place where all API endpoints are defined. This application uses fastapi, so each endpoint definition follows the fastapi format.

For more information check out the official [fastapi documentation](https://fastapi.tiangolo.com/tutorial/first-steps/).

## Usage

The following are the files inside this directory:

| Files                  | Purpose                                                                                        |
| ---------------------- | ---------------------------------------------------------------------------------------------- |
| `./deps.py`            | Dependency injection resources. Endpoints will inject dependencies using methods defined here. |
| `./api_v1/api.py`      | Application's route definition.                                                                |
| `./api_v1/endpoints/*` | API endpoint logic for each route.                                                             |

To create an endpoint, you need to:

1. Define a new route in [`api/api_v1/api.py`](./api_v1/api.py).
2. Create an endpoint file in `api/api_v1/endpoints/`. Define the route and the REST method.
3. Define the scope needed to use this endpoint, selecting or creating a new one from [`scopes`](../scopes/README.md) directory.
4. Define the request and response object schema, selecting or creating a new one from [`schemas`](../schemas/README.md) directory.
5. Define the API responsem selecting or creating a new one from [`responses`](../responses/README.md) directory.
6. If endpoint requires database transaction:
   - Create a new SQLAlchemy ORM model in [`models`](../models/README.md) directory if this requires a new entity or changes to an existing one.
   - Create a crud file in [`crud`](../crud/README.md) to store the transaction logic if there are any.
7. If there are complex domain logic and you wish to break it off into separate components, you can create it as a service module inside [`services`](../services/README.md) directory.
8. If there are global variables, put it in `utils/globals.py`.
9. If there are new environment variables, modify `core/config.py`.

The documentation for all the routes can either be found in the Google Docs spec sheet (please contact the backend team for specific ones) or the Swagger page. To access the Swagger page, run this application then navigate to `http://<url>/docs`.
