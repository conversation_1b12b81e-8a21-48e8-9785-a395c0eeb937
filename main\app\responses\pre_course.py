from pydantic import BaseModel


class PreCourseResponseClass(BaseModel):
    ADD_SUCCESS: dict = {
        "success": True,
        "message": "logged in successfully.",
    }

    EXISTS_ALREADY: dict = {
        "success": False,
        "message": "Question with same text already exists.",
    }

    # CREATE_SUCCESS: dict = {
    #     "success": True,
    #     "message": "accounts created successfully.",
    # }

    # CREATE_ACCOUNT_EXISTS: dict = {
    #     "success": False,
    #     "message": "account already exists.",
    # }

    # CREATE_TOKEN_INVALID: dict = {
    #     "success": False,
    #     "message": "invalid token or token expired.",
    # }

    # UPDATE_FAILED: dict = {
    #     "success": False,
    #     "message": "failed to update.",
    # }

    # TOKEN_REFRESH_SUCCESS: dict = {
    #     "success": True,
    #     "message": "token refreshed successfully.",
    # }

    # USER_NOT_EXIST: dict = {
    #     "success": True,
    #     "message": "No user found.",
    # }

    # ROLE_NOT_EXIST: dict = {
    #     "success": True,
    #     "message": "No role found.",
    # }

    QUESTION_DELETED_SUCCESS: dict = {
        "success": True,
        "message": "Question deleted successfully.",
    }

    ADD_UNSUCCESSFUL: dict = {
        "success": False,
        "message": "failed to add question.",
    }

    # INVITE_ROLE_INVALID: dict = {
    #     "success": False,
    #     "message": "invalid role",
    # }
    
    # USER_UPDATED: dict = {
    #     "success": True,
    #     "message": "The user updated successfully.",
    # }
    
    # PASSWORD_SENT: dict = {
    #     "success": True,
    #     "message": "New Password sent to your email.",
    # }
    
    # UPDATE_OWN_ACCOUNT: dict = {
    #     "success": True,
    #     "message": "You cannot update your own data.",
    # }
    
    # DELETE_OWN_ACCOUNT: dict = {
    #     "success": True,
    #     "message": "You cannot delete your own data.",
    # }

    # DELETE_FAILED: dict = {
    #     "success": False,
    #     "message": "failed to delete user.",
    # }
    

PreCourseResponses = PreCourseResponseClass()
