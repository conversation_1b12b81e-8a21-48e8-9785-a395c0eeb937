# !pip install openai==1.30.1
import cv2
import numpy as np
import os
import base64
import requests
import json
import gc

# Function to save collage
def save_collage(frames, collage_num, output_folder, frame_size):
    """
    Saves a collage of four frames.

    Args:
        frames (list): List of four frames.
        collage_num (int): The collage number.
        output_folder (str): The output folder path.
        frame_size (tuple): The size of a single frame (width, height).

    Returns:
        str: Path to the saved collage.
    """
    # Create an empty collage with the same size as a single frame
    collage = np.zeros((frame_size[1], frame_size[0], 3), dtype=np.uint8)
    
    # Resize each frame to fit in a 2x2 collage
    resized_frames = [cv2.resize(frame, (frame_size[0] // 2, frame_size[1] // 2)) for frame in frames]
    
    # Arrange the resized frames in a 2x2 manner
    collage[:frame_size[1] // 2, :frame_size[0] // 2] = resized_frames[0]
    collage[:frame_size[1] // 2, frame_size[0] // 2:] = resized_frames[1]
    collage[frame_size[1] // 2:, :frame_size[0] // 2] = resized_frames[2]
    collage[frame_size[1] // 2:, frame_size[0] // 2:] = resized_frames[3]
    
    collage_path = os.path.join(output_folder, f'collage_{collage_num}.jpg')
    cv2.imwrite(collage_path, collage)
    return collage_path


def get_collage_base64(frames, frame_size):
    """
    Saves a collage of four frames.

    Args:
        frames (list): List of four frames.
        frame_size (tuple): The size of a single frame (width, height).

    Returns:
        str: Base64 encoded string of the collage image.
    """
    # Create an empty collage with the same size as a single frame
    collage = np.zeros((frame_size[1], frame_size[0], 3), dtype=np.uint8)
    
    # Resize each frame to fit in a 2x2 collage
    resized_frames = [cv2.resize(frame, (frame_size[0] // 2, frame_size[1] // 2)) for frame in frames]
    
    # Arrange the resized frames in a 2x2 manner
    collage[:frame_size[1] // 2, :frame_size[0] // 2] = resized_frames[0]
    collage[:frame_size[1] // 2, frame_size[0] // 2:] = resized_frames[1]
    collage[frame_size[1] // 2:, :frame_size[0] // 2] = resized_frames[2]
    collage[frame_size[1] // 2:, frame_size[0] // 2:] = resized_frames[3]
    
    # Encode collage image to memory buffer
    _, buffer = cv2.imencode('.jpg', collage)
    
    # Convert buffer to base64 string
    collage_base64 = base64.b64encode(buffer).decode('utf-8')
    
    return collage_base64

# Function to create collages from video
def create_collages_from_video(video_path, output_folder):
    """
    Creates collages from video frames.

    Args:
        video_path (str): Path to the input video file.
        output_folder (str): Path to the output folder for collages.

    Returns:
        list: List of paths to the saved collages.
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("Error: Could not open video.")
        return []
    
    # Get frame dimensions
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    frame_size = (frame_width, frame_height)

    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    frames = []
    collage_num = 1
    frame_idx = 0
    collage_paths = []

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        if frame_idx % 5 == 0:  # Take every 5th frame
            frames.append(frame)
            if len(frames) == 4:
                collage_path = save_collage(frames, collage_num, output_folder, frame_size)
                collage_paths.append(collage_path)
                frames = []
                collage_num += 1
        frame_idx += 1

    # Handle remaining frames if less than 4
    if len(frames) > 0:
        while len(frames) < 4:
            frames.append(np.zeros_like(frames[0]))
        collage_path = save_collage(frames, collage_num, output_folder, frame_size)
        collage_paths.append(collage_path)

    cap.release()
    return collage_paths


# Function to create collages in base64 from video
def create_collages_base64_from_video(video_path):
    """
    Creates collages from video frames.

    Args:
        video_path (str): Path to the input video file.
        output_folder (str): Path to the output folder for collages.

    Returns:
        list: List of paths to the saved collages.
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("Error: Could not open video.")
        return []
    
    # Get frame dimensions
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    frame_size = (frame_width, frame_height)

    # if not os.path.exists(output_folder):
    #     os.makedirs(output_folder)

    frames = []
    collage_num = 1
    frame_idx = 0
    collages_base64 = []

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        if frame_idx % 5 == 0:  # Take every 5th frame
            frames.append(frame)
            if len(frames) == 4:
                collage_base64 = get_collage_base64(frames, frame_size)
                collages_base64.append(collage_base64)
                frames = []
                collage_num += 1
        frame_idx += 1

    # Handle remaining frames if less than 4
    if len(frames) > 0:
        while len(frames) < 4:
            frames.append(np.zeros_like(frames[0]))
        collage_base64 = get_collage_base64(frames, frame_size)
        collages_base64.append(collage_base64)

    cap.release()
    return collages_base64

# Function to encode image
def encode_image(image_path):
    """
    Encodes an image to a base64 string.

    Args:
        image_path (str): Path to the image file.

    Returns:
        str: Base64 encoded string of the image.
    """
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
    return encoded_string

# Function to analyze video frames
def analyze_video_frames(api_key = "***************************************************", base64_images=[]):
    """
    Analyzes video frames by sending them to the API.

    Args:
        api_key (str): API key for authentication.
        collage_paths (list): List of paths to the collage images.

    Returns:
        str: Analysis result from the API.
    """

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    messages = [
       {
          "role": "system",
          "content": f"""You are an expert in behavioral analysis.
          Your task is to evaluate the behavior of a representative responding to a public query based on their gestures, facial expressions, and overall demeanor. Give brief and to the point response."""
       },
       {
          "role": "user",
          "content": [
            {
              "type": "text",
              "text": f"""You are an AI model tasked with analyzing a sequence of video frames to determine the sentiment conveyed by the facial expressions, gestures, and overall demeanor of a government servant responding to a public query. 
                The possible sentiments to identify are: Neutral, Professional, Frustration, and Angry. The details for determining each sentiment are as follows:
                ```
                Neutral:
                    Facial Expressions: Relaxed facial muscles, no visible tension or negative emotions, neutral mouth position.
                    Gestures: Minimal hand movements, calm and steady posture.
                    Demeanor: Composed and calm, no signs of emotional disturbance.
                Professional:
                    Facial Expressions: Slight smile or neutral expression, steady eye contact, relaxed facial muscles.
                    Gestures: Controlled and deliberate hand movements, open and confident posture.
                    Demeanor: Polite and composed, demonstrating respect and attentiveness.
                Frustration:
                    Facial Expressions: Furrowed brows, slight frown, tightened lips.
                    Gestures: Repetitive or abrupt hand movements, restless shifting or tapping.
                    Demeanor: Signs of impatience, increased tension in posture, possible sighing or huffing.
                Angry:
                    Facial Expressions: Furrowed brows, glaring eyes, tight jaw, tense facial muscles.
                    Gestures: Aggressive or exaggerated hand movements, clenched fists, rigid posture.
                    Demeanor: Hostile or aggressive behavior, raised voice or sharp tone, signs of significant emotional agitation.
                ```
                Provide one line analysis on each facial expressions, gestures, and demeanor on the overall frames.
                Provide an overall analysis at the end, clearly distinguishing between bad behavior and professional behavior.
                Return the response in plain json string with keys as facial_expressions, gestures, demeanor and overall_analysis."""
            },
            *[
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_frame}",
                        "temperature": 0.0,
                        "detail": "low"
                    }
                } for base64_frame in base64_images
            ]
          ]
       }
    ]

    payload = {
        "model": "gpt-4o",
        "response_format": {"type": "json_object"},
        "messages": messages,
        "max_tokens": 1000
    }

    response = requests.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload)
    response_json = response.json()
    content = json.loads(response_json['choices'][0]['message']['content'])
    del base64_images
    return content


def process_video_and_analyze(video_path):
    api_key = "***************************************************"

    # Create collages from video
    collages_base64_list = create_collages_base64_from_video(video_path)
    analysis_result = analyze_video_frames(api_key, collages_base64_list)
    del collages_base64_list
    collected = gc.collect()
    print(f"Garbage collector: collected {collected} objects.")
    return {"video_analysis": analysis_result}

# Example usage
# if __name__ == "__main__":
#     video_path = "/home/<USER>/Downloads/vid_neg.mp4"
#     output_folder = "/home/<USER>/Downloads/collage"
#     api_key = "***************************************************"

#     # Create collages from video
#     collage_paths = create_collages_from_video(video_path, output_folder)

#     # Analyze the created collages
#     analysis_result = analyze_video_frames(api_key, collage_paths)
#     print(analysis_result)
