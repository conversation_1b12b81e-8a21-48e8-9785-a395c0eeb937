# Data Models

A collection of SQLAlchemy ORM models.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)
3. [Example](#example)

## Description

This directory contain objects that SQLAlchemy ORM uses to map to the corresponding database table.


Take a look at the documentation on how to:

- [Create a class that maps to a database table](https://docs.sqlalchemy.org/en/20/tutorial/metadata.html#declaring-mapped-classes).
- [Establish relationships between mapped objects](https://docs.sqlalchemy.org/en/20/tutorial/orm_related_objects.html).

For a more comprehensive look, check out the [official SQLAlchemy documentation](https://docs.sqlalchemy.org/en/20/tutorial/index.html) for more details.

## Usage

There should be a model file associated with each of the entity in that diagram. If there is a new entity, there should be a new model file to map to that table. If there are changes to the existing data model, then that change should be reflected in the corresponding model file.

The model object inherits the `Base` object, which handles all the boilerplate. So the only thing that needs to go into the class are `Columns` and `relationship` definitions.

Model objects in this directory are used by the transaction logic in the `crud` directory, as the query result will return an instance of the associated object.

The table below shows the file associated with each entity.

| Entity                       | File                                |
| ---------------------------- | ----------------------------------- |
| `Permission`                 | `permission.py`                     |
| `RolePermission`             | `role_permission.py`                |
| `Role`                       | `role.py`                           |
| `User`                       | `user.py`                           |
| `Animal`                     | `animal.py`                         |
| `BodyConditionScore`         | `body_condition_score.py`           |

## Example

This example is taken from `user.py`.

Assuming we want to map a class to the `User` table, we can create a `User` class. Normally we need to write some boilerplate code to establish the mapping. But instead of doing that, there is a Base class that we can inherit that does that for us. So the only thing that goes into the User class are the columns.

```python
# models/user.py

from sqlalchemy import Column, String, Date
from sqlalchemy.dialects.postgresql import TEXT

from app.db.base_class import Base


class User(Base):
    name = Column(
        String(40), comment="user name", nullable=False
    )
    email = Column(EmailType, unique=True, nullable=False)
    role_id = Column(
        Integer, ForeignKey("role.id", ondelete="RESTRICT"), nullable=False, index=True
    )

    hashed_password = Column(String(256), nullable=False)
    last_login = Column(DateTime, nullable=True)

    created_on = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    ...
```

Then import it to the `__init__.py` to help the rest of the application import from `models`:

```python
# models/__init__.py

from .user import User
```

Transaction logic in the `crud` directory will use this model object to interact with the database. Queries will return an instance of this object and transactions will take in the model object as an argument, as it is able to directly map to a specific row.

Below are a few interface methods that demonstrates this:

```python
# crud/crud_user.py

# Pydantic schemas that will be used to validate what type of object is allowed
# to be used to create and update the model.
from app.schemas import UserInvite, UserUpdate
from app import models


def create(self, db: Session, *, obj_in: UserInvite) -> models.User:
    pass


def get_by_id(db: Session, user_id: int) -> models.User | None:
    pass


def list(db: Session, filters: list[any]) -> list[models.User]:
    pass


def update(db: Session, *, db_obj: models.User, obj_in: UserUpdate) -> models.User:
    pass
```

An instance of the object is just a class with the same properties as the database table, with each property containing the data from a specific row. This means that this object could be use like any other object. Take for example:

```python
# endpoints/user.py

from app import crud


# Returns the Farm object.
query_result = crud.user.get_by_id(db, user_id)

```
