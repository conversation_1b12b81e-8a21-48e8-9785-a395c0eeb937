import cv2  # We're using OpenCV to read video, to install !pip install opencv-python
import base64
import time
from openai import OpenAI
import time
import gc
import os
import json
from .constants_listening_demo import base_prompt_listening
#from constants_all_modalities_updated1 import base_prompt
import numpy as np
from copy import deepcopy

# Load environment variables from .env file
#load_dotenv()

# Retrieve API key from environment variables
api_key = "***************************************************"
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY", api_key))

# response_struct_listening = json.dumps({"Staff's Behavior Analysis While Listening to the Query":{"Calm and Approachable Expression":"","Engaged Listening (Eye Contact)":"","No Signs of Frustration or Annoyance":"","Supportive Gestures (Hand Movements)":"","Open and Relaxed Facial Features":"No"},"Evaluation Summary":{"Overall Score":"","Evaluation":"","Summary":""}})

# response_struct_listening = json.dumps({"listening": {"facial_expressions": 4, "eye_contact": 5, "body_movement_and_posture": 4, "gestures": 4, "tone_and_manner_of_speech": 5, "choice_of_words": 5}, "you_did_well_at_the_following": ["", "", ""], "you_can_improve_by_focusing_on_the_following": ["", ""]})

response_struct_listening = json.dumps({"facialExpressions":{"calmAndApproachableExpression":"Yes/No","engagedListening":"Yes/No","noSignsOfFrustrationOrAnnoyance":"Yes/No","supportiveGestures":"Yes/No","openAndRelaxedFacialFeatures":"Yes/No"},"overallScore":"integer","feedback":{"positiveAreas":"<summary of areas where the person got high scores>","improvementSuggestions":"<suggestions based on areas where the person got low scores>"}})

def encode_image(image_path):
    """
    Encodes the image located at the given image_path into base64 format.
    Args:
        image_path (str): The path to the image file.
    Returns:
        str: The base64 encoded string representation of the image.
    Raises:
        FileNotFoundError: If the image file is not found at the specified path.
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def read_video(video_path):
    """
    Reads a video file and returns a list of base64 encoded frames.
    Args:
        video_path (str): The path to the video file.
    Returns:
        list: A list of base64 encoded frames.
    Raises:
        None
    Example:
        video_frames = read_video('/path/to/video.mp4')
    """

    video = cv2.VideoCapture(video_path)

    # Get the total number of frames
    total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"Total number of frames: {total_frames}")

    # Calculate frame interval to skip frames
    frame_interval = max(10, np.ceil(total_frames / 230))  # Ensuring the interval is at least 1 to avoid division by zero
    print(f"Frame interval: {frame_interval}")

    base64Frames = []
    frame_count = 0

    while video.isOpened():
        success, frame = video.read()
        if not success or len(base64Frames) >= 230:
            break
        if frame_count % frame_interval == 0:
            _, buffer = cv2.imencode(".jpg", frame)
            base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
        frame_count += 1

    video.release()
    print(len(base64Frames), "frames read.")
    return base64Frames



def process_listening_video(base64Frames):
    """
    Process a listening video by generating a text output using OpenAI's GPT-4o model.
    Args:
        base_prompt_listening (list): A list of prompts to be used as the base for generating the text output.
        base64Frames (list): A list of base64 encoded frames of the video.
        output_path (str): The path to save the generated text output.
    Returns:
        None
    """

    prompt = deepcopy(base_prompt_listening)
    prompt[-1]['content'].append(
            {
                "type": "text",
                "text": f"Please return response in json of format {response_struct_listening}."

            }
        )
    for frame in base64Frames:
        prompt[-1]['content'].append(
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{frame}",
                            "detail": "low"},
            }
        )
    params = {
            "model": "gpt-4o",
            "response_format": {"type": "json_object"},
            "messages": prompt,
            "temperature": 0.0,
            "max_tokens": 1000,
        }

    result = client.chat.completions.create(**params)
    if(result.choices[0].message.content == None):
        result = client.chat.completions.create(**params)
    output_text = result.choices[0].message.content
    # write_json(output_text, output_path)
    del prompt
    return output_text


def analyze_listening_part_demo(video_path):
    start_time = time.time()
    try:
        # Convert video to frames
        base64Frames = read_video(video_path)
        # Process and generate the output
        output = process_listening_video(base64Frames)
        print(output)
        output = json.loads(output)
        # sentiment_scores = [{"label": k, "value": v} for k, v in output.get("Sentiment Scores", {}).items()]
        # plot_graph(output_path_json)
        end_time = time.time()
        print(f"Listening video processing completed in {end_time - start_time:.2f} seconds")
        return output
    finally:
        del output
        del base64Frames
        collected = gc.collect()
        print(f"Garbage collector: collected {collected} objects.")