"""
This file contains all the API's related to 
permissions for different features
"""

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.responses import PermissionResponses, GeneralResponses
from app.scopes import PermissionScopes
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

router = APIRouter()


@router.post("/create", response_model=schemas.Permission)
def permission_create(
    *,
    permission_in: schemas.PermissionCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[PermissionScopes.create]
    ),
) -> JSONResponse:
    """Create new permissions
    scopes=[PermissionScopes.create]
    Args:
        permission_in (schemas.PermissionCreate): PermissionCreate Pydantic Model Permission to create
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user, scopes=[PermissionScopes.create]).

    Returns:
        JSONResponse: JSONResponse containing current permission
    """

    try:
        # Get payload & create permissions
        permission = crud.permission.create(db=db, obj_in=permission_in)
        # Return a Pydantic Validated Permission Model
        permission_dict = permission.to_dict()
        permission_dict = schemas.Permission(**permission_dict)
        permission_dict = jsonable_encoder(permission_dict)

        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="permission",
                message=str(PermissionResponses.PERMISSION_CREATE_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=201,
            content=APIResponse(
                **PermissionResponses.PERMISSION_CREATE_SUCCESS, data=permission_dict
            ),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.patch("/update/{permission_id}", response_model=schemas.Permission)
def pemrission_update(
    *,
    permission_id: int,
    payload_update_permission: schemas.PermissionUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[PermissionScopes.update]
    ),
) -> JSONResponse:
    """Update exiting permission

    Args:
        payload_update_permission (schemas.PermissionUpdate): PermissionUpdate Pydantic Model Permission to update
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing current permission
    """

    try:
        # Check if permission exists or not
        get_existing_permission = crud.permission.get_by_id(db=db, id=permission_id)

        if get_existing_permission:
            # update permission if exists
            permission = crud.permission.update(
                db=db, db_obj=get_existing_permission, obj_in=payload_update_permission
            )

            # Return a Pydantic Validated Permission Model
            permission_dict = permission.to_dict()
            permission_dict = schemas.Permission(**permission_dict)
            permission_dict = jsonable_encoder(permission_dict)

            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="permission",
                    message=str(PermissionResponses.PERMISSION_UPDATE_SUCCESS["message"]),
                )
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=APIResponse(
                    **PermissionResponses.PERMISSION_UPDATE_SUCCESS,
                    data=permission_dict,
                ),
            )
        logger.info(
            create_info_logging_message(
                endpoint="/update",
                feature="permission",
                message=str(GeneralResponses.DATA_DOES_NOT_EXITS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(**GeneralResponses.DATA_DOES_NOT_EXITS),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.delete("/delete/{permission_id}", response_model=schemas.Permission)
def permission_delete(
    *,
    permission_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[PermissionScopes.delete]
    ),
) -> JSONResponse:
    """Delete exiting permission

    Args:
        payload_delete_role (schemas.PermissionDelete): PermissionDelete Pydantic Model Permission to update
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing response message
    """

    try:
        # Check if permission exists or not
        get_permission = crud.permission.get_by_id(db=db, id=permission_id)
        if get_permission:
            # delete permission if exists
            permission = crud.permission.remove(db=db, id=get_permission.id)

            # Return a Pydantic Validated Permission Model
            permission_dict = permission.to_dict()
            permission_dict = schemas.Permission(**permission_dict)
            permission_out = jsonable_encoder(permission_dict)

            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="permission",
                    message=str(PermissionResponses.PERMISSION_DELETE_SUCCESS["message"]),
                )
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=APIResponse(
                    **PermissionResponses.PERMISSION_DELETE_SUCCESS,
                    data=permission_out,
                ),
            )
        logger.info(
            create_info_logging_message(
                endpoint="/delete",
                feature="permission",
                message=str(GeneralResponses.DATA_DOES_NOT_EXITS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(**GeneralResponses.DATA_DOES_NOT_EXITS),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/get/all", response_model=Page[schemas.Permission])
def permissions_get_all(
    db: Session = Depends(deps.get_db),
    # current_user: models.User = Security(
    #     deps.get_current_user, scopes=[PermissionScopes.read]
    # ),
) -> JSONResponse:
    """Get all permissions

    Args:
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing list of permissions
    """

    try:
        return paginate(db.query(models.Permission))

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/get/{permission_id}", tags=["permission"])
def permission_get(
    *,
    permission_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[PermissionScopes.read]
    ),
) -> JSONResponse:
    """Fetch all permission

    Args:
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing all permissions
    """

    try:
        permission = crud.permission.get(db=db, id=permission_id)
        if not permission:
            logger.info(
                create_info_logging_message(
                    endpoint="/get",
                    feature="permission",
                    message=str(GeneralResponses.DATA_DOES_NOT_EXITS["message"]),
                )
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=APIResponse(**GeneralResponses.DATA_DOES_NOT_EXITS),
            )

        # Return a Pydantic Validated Permission Model
        permission_dict = permission.to_dict()
        permission_dict = schemas.Permission(**permission_dict)
        permission_dict = jsonable_encoder(permission_dict)

        logger.info(
            create_info_logging_message(
                endpoint="/get",
                feature="permission",
                message=str(PermissionResponses.GET_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(
                **PermissionResponses.GET_SUCCESS, data=permission_dict
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
