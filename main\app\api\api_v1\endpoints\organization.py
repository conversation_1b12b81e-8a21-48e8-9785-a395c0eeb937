"""
This file contains all the API's related to organization
"""

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.scopes import OrganizationScopes, UserScopes
from app.responses import GeneralResponses
from app.logger import logger
from app.utils.globals import create_info_logging_message, AvailableRoles
from app.models.organization import OrganizationStatus
from app.core.security import get_password_hash

from fastapi import APIRouter, Depends, Security, status, Query
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from typing import List, Optional

router = APIRouter()


@router.post("/create", response_model=schemas.Organization)
def create_organization(
    *,
    organization_in: schemas.OrganizationCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[OrganizationScopes.create]
    ),
) -> JSONResponse:
    """Create new organization

    Args:
        organization_in (schemas.OrganizationCreate): OrganizationCreate Pydantic Model
        db (Session): SQLAlchemy Session
        current_user (User): Current User with create organization permission

    Returns:
        JSONResponse: JSONResponse containing created organization
    """

    try:
        # Create organization with pending status
        organization_data = organization_in.dict()
        organization_data["status"] = OrganizationStatus.PENDING
        organization = models.Organization(**organization_data)
        db.add(organization)
        db.commit()
        db.refresh(organization)
        
        # Return organization data
        organization_dict = schemas.Organization.from_orm(organization)
        organization_out = jsonable_encoder(organization_dict)

        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="organization",
                message=f"Organization {organization.name} created successfully",
            )
        )
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content=APIResponse(
                success=True,
                message="Organization created successfully.",
                data=organization_out
            ),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/get/{organization_id}", response_model=schemas.Organization)
def get_organization(
    *,
    organization_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[OrganizationScopes.read]
    ),
) -> JSONResponse:
    """Get organization by ID

    Args:
        organization_id (int): Organization ID
        db (Session): SQLAlchemy Session
        current_user (User): Current User with read organization permission

    Returns:
        JSONResponse: JSONResponse containing organization data
    """

    try:
        organization = db.query(models.Organization).filter(models.Organization.id == organization_id).first()
        if not organization:
            raise APIException(
                status_code=status.HTTP_404_NOT_FOUND,
                message=APIResponse(
                    success=False,
                    message="Organization not found.",
                ),
            )
        
        organization_dict = schemas.Organization.from_orm(organization)
        organization_out = jsonable_encoder(organization_dict)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(
                success=True,
                message="Organization fetched successfully.",
                data=organization_out
            ),
        )
    except APIException as e:
        raise e
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/get/all", response_model=Page[schemas.Organization])
def get_all_organizations(
    *,
    status: Optional[str] = Query(None, description="Filter by organization status"),
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[OrganizationScopes.read]
    ),
) -> Page:
    """Get all organizations with optional status filter

    Args:
        status (str, optional): Filter by organization status
        db (Session): SQLAlchemy Session
        current_user (User): Current User with read organization permission

    Returns:
        Page: Paginated list of organizations
    """

    try:
        query = db.query(models.Organization)
        
        # Apply status filter if provided
        if status:
            try:
                status_enum = OrganizationStatus(status)
                query = query.filter(models.Organization.status == status_enum)
            except ValueError:
                # Invalid status value, ignore filter
                pass
                
        return paginate(query)
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.patch("/update/{organization_id}", response_model=schemas.Organization)
def update_organization(
    *,
    organization_id: int,
    organization_in: schemas.OrganizationUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[OrganizationScopes.update]
    ),
) -> JSONResponse:
    """Update organization details

    Args:
        organization_id (int): Organization ID
        organization_in (schemas.OrganizationUpdate): OrganizationUpdate Pydantic Model
        db (Session): SQLAlchemy Session
        current_user (User): Current User with update organization permission

    Returns:
        JSONResponse: JSONResponse containing updated organization
    """

    try:
        organization = db.query(models.Organization).filter(models.Organization.id == organization_id).first()
        if not organization:
            raise APIException(
                status_code=status.HTTP_404_NOT_FOUND,
                message=APIResponse(
                    success=False,
                    message="Organization not found.",
                ),
            )
        
        # Update organization fields
        update_data = organization_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(organization, field, value)
            
        db.commit()
        db.refresh(organization)
        
        organization_dict = schemas.Organization.from_orm(organization)
        organization_out = jsonable_encoder(organization_dict)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(
                success=True,
                message="Organization updated successfully.",
                data=organization_out
            ),
        )
    except APIException as e:
        raise e
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.patch("/update-status/{organization_id}", response_model=schemas.Organization)
def update_organization_status(
    *,
    organization_id: int,
    status_update: schemas.OrganizationStatusUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[OrganizationScopes.update_status]
    ),
) -> JSONResponse:
    """Update organization status

    Args:
        organization_id (int): Organization ID
        status_update (schemas.OrganizationStatusUpdate): Status update data
        db (Session): SQLAlchemy Session
        current_user (User): Current User with update_status organization permission

    Returns:
        JSONResponse: JSONResponse containing updated organization
    """

    try:
        organization = db.query(models.Organization).filter(models.Organization.id == organization_id).first()
        if not organization:
            raise APIException(
                status_code=status.HTTP_404_NOT_FOUND,
                message=APIResponse(
                    success=False,
                    message="Organization not found.",
                ),
            )
        
        # Update organization status
        organization.status = status_update.status
        db.commit()
        db.refresh(organization)
        
        organization_dict = schemas.Organization.from_orm(organization)
        organization_out = jsonable_encoder(organization_dict)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(
                success=True,
                message=f"Organization status updated to {status_update.status}.",
                data=organization_out
            ),
        )
    except APIException as e:
        raise e
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.post("/create-admin/{organization_id}", response_model=schemas.UserOut)
def create_organization_admin(
    *,
    organization_id: int,
    user_data: schemas.UserInvite,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[OrganizationScopes.create_admin]
    ),
) -> JSONResponse:
    """Create an admin user for an organization

    Args:
        organization_id (int): Organization ID
        user_data (schemas.UserInvite): User data for the new admin
        db (Session): SQLAlchemy Session
        current_user (User): Current User with create_admin organization permission

    Returns:
        JSONResponse: JSONResponse containing created user
    """

    try:
        # Check if organization exists
        organization = db.query(models.Organization).filter(models.Organization.id == organization_id).first()
        if not organization:
            raise APIException(
                status_code=status.HTTP_404_NOT_FOUND,
                message=APIResponse(
                    success=False,
                    message="Organization not found.",
                ),
            )
        
        # Check if organization is approved
        if organization.status != OrganizationStatus.APPROVED:
            raise APIException(
                status_code=status.HTTP_400_BAD_REQUEST,
                message=APIResponse(
                    success=False,
                    message="Cannot create admin for organization that is not approved.",
                ),
            )
        
        # Get admin role ID
        admin_role = db.query(models.Role).filter(models.Role.name == AvailableRoles.admin).first()
        if not admin_role:
            raise APIException(
                status_code=status.HTTP_400_BAD_REQUEST,
                message=APIResponse(
                    success=False,
                    message="Admin role not found.",
                ),
            )
        
        # Create admin user
        hashed_password = get_password_hash(user_data.password)
        user = models.User(
            name=user_data.name,
            email=user_data.email,
            role_id=admin_role.id,
            hashed_password=hashed_password,
            organization_id=organization_id,
            organization_name=organization.name,
            role_in_organization="admin",
            IC=f"ORG-{organization_id}-ADMIN-{user_data.email.split('@')[0]}"
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        user_dict = schemas.UserOut.from_orm(user)
        user_out = jsonable_encoder(user_dict)

        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content=APIResponse(
                success=True,
                message="Organization admin created successfully.",
                data=user_out
            ),
        )
    except APIException as e:
        raise e
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
