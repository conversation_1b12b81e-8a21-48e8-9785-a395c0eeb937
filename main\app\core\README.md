# Access Management

A collection of components that manages the application's account and resource access.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)
3. [Example](#example)

## Description

This directory provides resource access mechanisms for 2 different context:

- A user accessing the application's resources.
- The application accessing third-party resource configurations.

This application require components that helps with resource access. Without a way to access different resources, the application is not useful to anyone, which is why directory is called `core`.

## Usage

### Users Accessing the Application

The following files needed to grant users access to the application:

| Files         | Purpose                                                                                                     |
| ------------- | ----------------------------------------------------------------------------------------------------------- |
| `auth.py`     | Provide ways to authenticate a user and authorize the resources they can access. _(user/password and JWT)._ |_                                               |
| `security.py` | Provide ways to verify credentials. _(password hash)_                                                       |
| `scopes.py` | Gets all the Scopes/Permissions from the DB.                                                       |
| `user.py`     | Provide ways for the user to have access to the application. _(email or sms invitation)._                   |

You shouldn't touch these files unless there is a change in user login mechanisms.

### Configuring Third-party Resources

Configurations needed to access third-party resources are taken from the machine's environment variable. This is provided in the `dev.env` file located in the root directory of this repository. For example, these may be database connection information, API keys, etc.

The followung are the files that provides the application access to environement variables"

| Files       | Purpose                                                                                                                                                                |
| ----------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `dev.env`   | Define all the environment variables. Located in the root directory of this repo.                                                                                      |
| `config.py` | Provides a component that helps the rest of the application access to environment variables without having to use operating system methods. Located in this directory. |

You need to modify this file every time you want to add new environment variables and want the application to have access to it.

## Example

We will only be showing how to modify `config.py` to add support for new environment variables, as user access mechanisms rarely changes.

To add support for new environment variables you need to:

1. Define the variable in `dev.env`.

   ```bash
   # dev.env

   NEW_VARIABLE=123
   NEW_VARIABLE_TWO="abc"
   ```

3. Add a config class in `config.py`. Specify the type and the default values.

   ```python
   # core/config.py
   from pydantic import BaseSettings


   class NewVariables(BaseSettings):
       """New Variables Settings"""

       NEW_VARIABLE: int = 0
       NEW_VARIABLE_TWO: str = ""
   ```

4. Add an instance of that config class in the main config class in `config.py`. This is located at the bottom of the file.

   ```python
   # core/config.py


   class Settings(BaseSettings):
       """Settings"""

       # Other configs

       new_variables = NewVariables()


    settings = Settings()
   ```

5. To use it, simply import the config object and refer to the variable.

   ```python
   # Some file
   import app.core.config import settings

   new_variable_two = settings.new_variables.NEW_VARIABLE_TWO
   ```

Pydantic automatically finds the environment variables that have the same name as the ones you define incide the `BaseSettings` class then inject it into the application. As you can see, there is no need to use os methods to get the env variables.
