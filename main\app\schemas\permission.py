"""
This file contains all the Schemas 
of Permissions related feature
"""

from pydantic import BaseModel
from datetime import datetime


class PermissionBase(BaseModel):
    name: str


class PermissionCreate(PermissionBase):
    ...


class PermissionUpdate(PermissionBase):
    name: str


class PermissionInDBBase(PermissionBase):
    id: int | None = None
    created_on: datetime
    last_updated: datetime

    class Config:
        orm_mode = True


class Permission(PermissionInDBBase):
    ...
