import os
import sys
from datetime import datetime

from fastapi.exception_handlers import (
    request_validation_exception_handler,
)
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.exceptions import RequestValidationError
from starlette.requests import Request

sys.path.append(os.getcwd())
# Prevent display of "The Zen of Python" during server startup
original_stdout = sys.stdout
sys.stdout = open(os.devnull, "w")

from pathlib import Path

from fastapi import FastAPI, APIRouter, HTTPException, status, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi_pagination import add_pagination

from app.api.api_v1.api import api_router
from app.core.config import settings
from app.responses.base import APIException

# imports for authentication of fast api docs URL
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
# from api_analytics.fastapi import Analytics
from app.logger import logger  # Import the logger from the logger.py module

import secrets

from fastapi.security import HTTPBasic, HTTPBasicCredentials


BASE_PATH = Path(__file__).resolve().parent

root_router = APIRouter()

from dotenv import load_dotenv

load_dotenv()

ENVIRONMENT = os.environ.get("APP_ENVIRONMENT")
security = HTTPBasic()

app = FastAPI(
    title="Digital Coach",
    version="0.1.0",
    openapi_url=None,
    docs_url=None,
    redoc_url=None,
    swagger_ui_parameters={"defaultModelsExpandDepth": -1},
)

# Restore the original stdout, after successfull
# initialization of app
sys.stdout.close()
sys.stdout = original_stdout

app.include_router(api_router, prefix=settings.API_V1_STR)
app.include_router(root_router)
add_pagination(app)

# logging.config.fileConfig(f"{os.path.dirname(__file__)}/logging.conf")
# app.add_middleware(
#     Analytics, api_key=os.environ.get("FASTAPI_ANALYTICS_API_KEY")
# )  # middleware for fast api analytics


# if settings.BACKEND_CORS_ORIGINS:
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173", 
        "https://ial.zaheen.ai", 
        "https://vira-vision.vercel.app",
        "https://gem-woad-pi.vercel.app",
        "https://gem.gemlearning.app",
        "https://gem-staging.gemlearning.app",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class CustomMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = datetime.utcnow()
        logger.info(f"Incoming request: {request.method} {request.url}")

        response = await call_next(request)

        end_time = datetime.utcnow()
        response_time = (end_time - start_time).total_seconds() * 1000
        logger.info(
            f"Response status code: {response.status_code}. Response time: {response_time:.2f}ms"
        )
        return response


# Add the middleware to the FastAPI app
app.add_middleware(CustomMiddleware)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    print(f"OMG! The client sent invalid data!: {exc}")
    return await request_validation_exception_handler(request, exc)


@app.exception_handler(APIException)
async def api_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.message,
        headers=exc.headers,
    )


def get_current_username(credentials: HTTPBasicCredentials = Depends(security)):
    # if we are running in localhost don't require password to see the docs
    if ENVIRONMENT == 'local':
        return "dev_user"

    # // other wise check the creds
    SWAGGER_USERNAME = os.environ.get("SWAGGER_USERNAME")
    SWAGGER_PASSWORD = os.environ.get("SWAGGER_PASSWORD")
    if not SWAGGER_PASSWORD or not SWAGGER_USERNAME:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Please set swagger username and password in your environment",
            headers={"WWW-Authenticate": "Basic"},
        )
    correct_username = secrets.compare_digest(
        credentials.username, SWAGGER_USERNAME
    )
    correct_password = secrets.compare_digest(
        credentials.password, SWAGGER_PASSWORD
    )
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username


@app.get("/", include_in_schema=False)
async def default():
    logger.info("Logging a message in the endpoint.")
    return {"Server Status": "Server is working fine..."}


@app.get("/docs", include_in_schema=False)
async def get_swagger_documentation(username: str = Depends(get_current_username)):
    return get_swagger_ui_html(openapi_url="/openapi.json", title="docs")


@app.get("/redoc", include_in_schema=False)
async def get_redoc_documentation(username: str = Depends(get_current_username)):
    return get_redoc_html(openapi_url="/openapi.json", title="docs")


@app.get("/openapi.json", include_in_schema=False)
async def openapi(username: str = Depends(get_current_username)):
    return get_openapi(title=app.title, version=app.version, routes=app.routes)



if __name__ == "__main__":
    # Use this for debugging purposes only
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=5000, reload=True)
