"""
This file contains the model mapping of 
organization table
"""

from datetime import datetime
from enum import Enum

from app.db.base_class import Base

from sqlalchemy import Column, DateTime, String, Integer, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship


class OrganizationStatus(str, Enum):
    APPROVED = "approved"
    HOLD = "hold"
    PENDING = "pending"


class Organization(Base):
    name = Column(
        String(100), comment="organization name", nullable=False, unique=True
    )
    description = Column(
        String(500), comment="organization description", default="", nullable=True
    )
    status = Column(
        SQLAlchemyEnum(OrganizationStatus),
        default=OrganizationStatus.PENDING,
        nullable=False,
        comment="organization status"
    )
    address = Column(
        String(200), comment="organization address", default="", nullable=True
    )
    contact_email = Column(
        String(100), comment="organization contact email", default="", nullable=True
    )
    contact_phone = Column(
        String(20), comment="organization contact phone", default="", nullable=True
    )
    users = relationship("User", back_populates="organization")
    created_on = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
