# Validation Schemas

A collection of Pydantic schemas to be used for validation.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)
3. [Example](#example)
   1. [Validating Request](#validating-request)
   2. [Validating Response](#validating-response)

## Description

Files in directory contain Pydantic model classes that are used for validation. This help some methods and classes to be strongly typed, which helps to prevent bugs. Schema objects also acts as a contract between a method and its client, guaranteeing to return output object with a certain structure and receiving argument objects with a certain structure.

Take a look at the official documentation on [creating a Pydantic model](https://docs.pydantic.dev/usage/models/).

## Usage

When creating an API endpoint, the API request and response must have an associating schema object. This decouples the validation logic and delegate it to Pydantic. This guarantees that the client will be forced to send a request object that have a structure defined by the request schema. This also forces our endpoint to send only a response object that have a structure defined by the response schema. This creates a two-way contract gets rid of superfluous validation logic.

Aside from creating an endpoint request and response schema, there should also be a schema for each state of each entity. Schema objects are organized into modules that corresponds to an entity.

Creating a schema simply requires us to create a class that inherits Pydantic's `BaseModel`. The properties of the Pydantic model class is the object structure. We can use this as a method argument, method return type, or validator.

## Example

This example is taken from the User endpoint

### Validating Request

Assuming we want to have an endpoint that creates a User entity. The client must provide us with the information needed to create a new User. We can define a schema that helps us enforce what those information should be:

```python
# schemas/user.py

from pydantic import BaseModel, constr


class UserBase(BaseModel):
    name: str | None
    email: EmailStr | None
    role_id: int | None

    ...
```

But remember that we need a schema for different state of the entity. The current state is the "input request" state, therefore we need to create another schema that uses `UserBase` as the base and add extra fields, if there are any.

In this case, we want the client to send all the User information, therefore we don't need to add anything to the new schema object.

```python
# schemas/User.py

class UserInDBBase(UserBase):
    id: int | None = None
    last_login: datetime | None = None
    created_on: datetime | None
    last_updated: datetime | None

    class Config:
        orm_mode = True
```

Then import it to the `__init__.py` to help the rest of the application import from `schemas`:

```python
# schemas/__init__.py

from .user import (
    UserBase,
    UserInDBBase,
)
```

We can now use this schema to define the expected request object type on the endpoint. If the client sends a request object with an invalid structure, then they will receive an error message.

```python
# endpoints/user.py

from fastapi import Depends
from starlette.responses import JSONResponse

from app import models, schemas


@router.post("/create")
def user_create(*,
    background_tasks: BackgroundTasks,
    request_data: schemas.UserInvite,
    db: Session = Depends(deps.get_db),
    email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.invite]
    ),) -> JSONResponse:
    pass
```

### Validating Response

Let's say we want to send a response object that contain the newly created user's metadata, we can define the response schema like:

```python
# schemas/user.py

class UserOut(UserInviteOut):
    id: int
    created_on: datetime | None
    last_updated: datetime | None

```

We can decouple this, as there are two parts to this: metadata from the database and metadata in regards to the User entity itself.

```python
# schemas/user.py

class UserInDBBase(UserBaseBase):
    ...

```

Then import it to the `__init__.py` to help the rest of the application import from `schemas`:

```python
# schemas/__init__.py

from .user import (
    UserBase,
    UserInDBBase,
    UserOut,
)
```

We can now use this to validate result before sending it as a response data. If the validation fails, then the endpoint returns an error.

```python
# endpoints/user.py

from fastapi import Depends
from fastapi.encoders import jsonable_encoder
from starlette.responses import JSONResponse

from app import models, schemas
from app.responses import UserResponses
from app.responses.base import APIResponse


@router.post("/invite")
def user_invite(
    background_tasks: BackgroundTasks,
    request_data: schemas.UserInvite,
    db: Session = Depends(deps.get_db),
    email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.invite]
    ),) -> JSONResponse:

    # Everything is good, create the user!
    user = crud.user.create(db=db, obj_in=request_data)

    # Validate the result
    user_out = schemas.UserOut(**user.to_dict())

    # Encode User data as JSON
    user_out = jsonable_encoder(user_out)

    # Return response object
    return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponses.USER_CREATE_SUCCESS,
                data={"user": user_out},
            ),
        )
```
