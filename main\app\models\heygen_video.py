"""
This file contains the model mapping of HeygenVideo
"""

from datetime import datetime, timezone

from app.db.base_class import Base

from sqlalchemy import (
    Column,
    DateTime,
    String,
    Integer,
    ForeignKey
)
from sqlalchemy.orm import relationship


class HeygenVideo(Base):
    """
    Model for storing Heygen-generated videos with instructor ownership
    """
    name = Column(String, nullable=False, comment="Name of the Heygen video")
    s3_path = Column(String, nullable=False, comment="Path of the video file in S3 bucket")
    public_url = Column(String, nullable=True, comment="Public URL of the video if available")
    instructor_id = Column(Integer, ForeignKey('user.id'), nullable=False, comment="ID of the instructor who created this video")
    instructor = relationship('User', back_populates='heygen_videos')
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                        onupdate=lambda: datetime.now(timezone.utc), nullable=False)
