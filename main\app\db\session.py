from app.core.config import settings
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

connection_uri = settings.db.DATABASE_CONNECTION_URL

if "sqlite" in connection_uri:
    
    db_engine = create_engine(
        connection_uri, connect_args={"check_same_thread": False}
    )
else:
    db_engine = create_engine(
        connection_uri,
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=db_engine)
