"""
This file contains all the API's related to conversation
"""

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.services import EmailClient
from app.scopes import InstructorAnalyticsScopes, UserScopes
from app.responses import AnalyticsResponses, GeneralResponses, AuthResponses, RoleResponses
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, cast, Date, extract
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import parse_obj_as
from typing import List
from datetime import datetime, timedelta
from collections import Counter

router = APIRouter()


# Function to calculate average time
def calculate_average_time(time_intervals):
    total_duration = timedelta()
    for start_time, end_time in time_intervals:
        duration = end_time - start_time
        total_duration += duration

    average_time = total_duration / len(time_intervals) if time_intervals else 0
    return average_time
    

# Function to find the most used tag(s)
def find_most_used_tag(data):
    tags = [entry['tag'] for entry in data]
    tag_counts = Counter(tags)
    if tag_counts:
        max_count = max(tag_counts.values())
        
        # Find all tags with the max count
        most_used_tags = [tag for tag, count in tag_counts.items() if count == max_count]
    else:
        max_count = 0
        most_used_tags = []
    
    # Join tags with a comma if there are multiple
    return ', '.join(most_used_tags)


@router.get(
    "/instructor/overview",
    status_code=status.HTTP_200_OK,
)
def instructor_analytics_overview(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[InstructorAnalyticsScopes.read, UserScopes.read]
    ),
) -> JSONResponse:
    """
    Instructor Analytics Overview page
    """
    try:
        user_role = crud.role.get(db=db, id=current_user.role_id).name
        if user_role == "instructor":
            total_courses = db.query(models.Course).filter(models.Course.instructor == current_user.id).count()
            total_students = db.query(models.CourseProgress).filter(models.CourseProgress.course_id.in_([i[0] for i in db.query(models.Course).with_entities(models.Course.id).filter(models.Course.instructor == current_user.id).all()])).count()
            total_students_completed_courses = db.query(models.CourseProgress).filter(and_(models.CourseProgress.progress_percentage >= 100, models.CourseProgress.course_id.in_([i[0] for i in db.query(models.Course).with_entities(models.Course.id).filter(models.Course.instructor == current_user.id).all()]))).count()
            total_students_remaining_courses = db.query(models.CourseProgress).filter(and_(models.CourseProgress.progress_percentage < 100, models.CourseProgress.course_id.in_([i[0] for i in db.query(models.Course).with_entities(models.Course.id).filter(models.Course.instructor == current_user.id).all()]))).count()
        else:
            total_courses = db.query(models.Course).count()
            total_students = db.query(models.CourseProgress).filter(models.CourseProgress.course_id.in_([i[0] for i in db.query(models.Course).with_entities(models.Course.id).all()])).count()
            total_students_completed_courses = db.query(models.CourseProgress).filter(and_(models.CourseProgress.progress_percentage >= 100, models.CourseProgress.course_id.in_([i[0] for i in db.query(models.Course).with_entities(models.Course.id).all()]))).count()
            total_students_remaining_courses = db.query(models.CourseProgress).filter(and_(models.CourseProgress.progress_percentage < 100, models.CourseProgress.course_id.in_([i[0] for i in db.query(models.Course).with_entities(models.Course.id).all()]))).count()
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **AnalyticsResponses.GET_INSTRUCTOR_OVERVIEW,
                data={
                    "total_courses": total_courses,
                    "total_students": total_students,
                    "total_students_completed_courses": total_students_completed_courses,
                    "total_students_remaining_courses": total_students_remaining_courses,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/instructor/login_user",
    status_code=status.HTTP_200_OK,
)
def instructor_analytics_login_user(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[InstructorAnalyticsScopes.read]
    ),
) -> JSONResponse:
    """
    Instructor Analytics Login User page
    """
    try:
        time_of_day_most_users_active = (db.query(
                extract("hour", models.User.last_login).label("hour")
            )
            .filter(and_(models.User.last_login.isnot(None), models.User.id.in_([i[0] for i in db.query(models.Course).with_entities(models.User.id).filter(models.Course.instructor == current_user.id).all()])))
            .group_by(extract("hour", models.User.last_login))
            .order_by(func.count(models.User.id).desc())
            .first())
        
        active_users = db.query(
                func.count(models.User.id).label("active_users")
            ).filter(and_(models.User.last_login.isnot(None), models.User.id.in_([i[0] for i in db.query(models.Course).with_entities(models.User.id).filter(models.Course.instructor == current_user.id).all()]))).group_by(cast(models.User.last_login, Date)).all()
        active_users = active_users[0][0] if active_users else 0
        users_accessed_course = db.query(models.CourseProgress).filter(models.CourseProgress.course_id.in_([i[0] for i in db.query(models.Course).with_entities(models.Course.id).filter(models.Course.instructor == current_user.id).all()])).count()
        if time_of_day_most_users_active:
            time_of_day_most_users_active = str(time_of_day_most_users_active[0])
            time_of_day_most_users_active = time_of_day_most_users_active + (" PM" if int(time_of_day_most_users_active) >= 12 else ' AM')
        else:
            time_of_day_most_users_active = "N/A"
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **AnalyticsResponses.GET_INSTRUCTOR_OVERVIEW,
                data={
                    "active_users": active_users,
                    "users_accessed_course": users_accessed_course,
                    "average_user_session": 19, # hard code for now, how to get average user session
                    "time_of_day_most_users_active": time_of_day_most_users_active,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/instructor/plunge",
    status_code=status.HTTP_200_OK,
)
def instructor_analytics_plunge(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[InstructorAnalyticsScopes.read]
    ),
) -> JSONResponse:
    """
    Instructor Analytics Plunge Activity page
    """
    try:
        plung_activities_id_list = [i[0] for i in db.query(models.Activity).with_entities(models.Activity.id).filter(models.Activity.type == models.ActivityType.plunge_activity).all()]
        courses_id_list = [i[0] for i in db.query(models.Course).with_entities(models.Course.id).filter(models.Course.instructor == current_user.id).all()]
        plunge_activity_analysis = db.query(models.ActivityProgress).with_entities(models.ActivityProgress.user_input, models.ActivityProgress.retries, models.ActivityProgress.created_at, models.ActivityProgress.updated_at).filter(and_(models.ActivityProgress.course_id.in_(courses_id_list), models.ActivityProgress.activity_id.in_(plung_activities_id_list))).all()
        total_replay_time = 0
        user_input = []
        start_end_time_list = []
        
        for i in plunge_activity_analysis:
            start_end_time_list.append((i[2], i[3]))
            total_replay_time = total_replay_time + i[1]
            user_input.extend(i[0])
            
        
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **AnalyticsResponses.GET_INSTRUCTOR_OVERVIEW,
                data={
                    "most_used_pharase": str(find_most_used_tag(user_input)),
                    "average_time_to_complete": str(calculate_average_time(start_end_time_list)),
                    "total_replay_time": total_replay_time,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/instructor/quiz",
    status_code=status.HTTP_200_OK,
)
def instructor_analytics_quiz(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[InstructorAnalyticsScopes.read]
    ),
) -> JSONResponse:
    """
    Instructor Analytics Quiz Activity page
    """
    try:
        quiz_activities_id_list = [i[0] for i in db.query(models.Activity).with_entities(models.Activity.id).filter(models.Activity.type == models.ActivityType.quiz).all()]
        courses_id_list = [i[0] for i in db.query(models.Course).with_entities(models.Course.id).filter(models.Course.instructor == current_user.id).all()]
        quiz_activity_analysis = db.query(models.ActivityProgress).with_entities(models.ActivityProgress.result, models.ActivityProgress.retries, models.ActivityProgress.created_at, models.ActivityProgress.updated_at).filter(and_(models.ActivityProgress.course_id.in_(courses_id_list), models.ActivityProgress.activity_id.in_(quiz_activities_id_list))).all()
        total_replay_time = 0
        total_score = 0
        correct_score = 0
        start_end_time_list = []
        
        for i in quiz_activity_analysis:
            start_end_time_list.append((i[2], i[3]))
            total_replay_time = total_replay_time + i[1]
            correct_score += i[0]['correct']
            total_score += i[0]['incorrect']
            total_score += i[0]['correct']

        
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **AnalyticsResponses.GET_INSTRUCTOR_OVERVIEW,
                data={
                    "average_score": f"{correct_score}/{total_score}",
                    "average_time_to_complete": str(calculate_average_time(start_end_time_list)),
                    "total_replay_time": total_replay_time,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

