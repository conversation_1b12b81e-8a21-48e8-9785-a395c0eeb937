from pydantic import BaseModel


class CourseResponsesClass(BaseModel):

    CREATE_SUCCESS: dict = {
        "success": True,
        "message": "Course created successfully.",
    }

    CREATE_COURSE_EXISTS: dict = {
        "success": False,
        "message": "Course already exists.",
    }
    
    CREATE_FAILED: dict = {
        "success": False,
        "message": "Failed to create course.",
    }

    UPDATE_FAILED: dict = {
        "success": False,
        "message": "Failed to update.",
    }

    COURSE_NOT_EXIST: dict = {
        "success": False,
        "message": "No course found.",
    }

    COURSE_DELETED_SUCCESS: dict = {
        "success": True,
        "message": "Course deleted successfully.",
    }
    
    COURSE_UPDATED: dict = {
        "success": True,
        "message": "Course updated successfully.",
    }

    DELETE_FAILED: dict = {
        "success": False,
        "message": "Failed to delete course.",
    }

    SUCCESSFULLY_ENROLLED: dict = {
        "success": True,
        "message": "User is successfully enrolled in course.",
    }

    ALREADY_ENROLLED: dict = {
        "success": False,
        "message": "User is already enrolled in course.",
    }

CourseResponses = CourseResponsesClass()
