"""
This file contains all the API's related to
user
"""

import random

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.services import EmailClient
from app.core.user import is_valid_email, send_invitation_email, check_user_role, check_file_type_and_content, generate_password, \
    calculate_file_hash
from app.core.security import get_password_hash, verify_password
from app.scopes import UserScopes, AuthScopes
from app.responses import UserResponses, GeneralResponses, AuthResponses, RoleResponses
from app.logger import logger
from app.utils.globals import create_info_logging_message, AvailableRoles
from app.models.organization import OrganizationStatus

from fastapi import APIRouter, Depends, Security, status, BackgroundTasks, UploadFile, File, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy.exc import PendingRollbackError
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from typing import List

import pandas as pd
from datetime import datetime

router = APIRouter()


@router.post(
    "/invite",
    status_code=status.HTTP_200_OK,
    response_model=schemas.UserOut,
)
def user_invite(
    *,
    background_tasks: BackgroundTasks,
    request_data: schemas.UserInvite,
    db: Session = Depends(deps.get_db),
    email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.invite]
    ),
) -> JSONResponse:
    """Invite a new user by email for a specific role

    Args:\n
        request_data


    """
    # Enforce users can invite roles, same as them, or below them
    deps.current_user_role = crud.role.get_by_id(db, id=current_user.role_id)
    invite_role = crud.role.get_by_id(db, id=request_data.role_id)
    if not invite_role:
        logger.info(
            create_info_logging_message(
                endpoint="/invite",
                feature="user",
                message=str(UserResponses.INVITE_ROLE_INVALID["message"]),
            )
        )
        return JSONResponse(
            status_code=401,
            content=APIResponse(**UserResponses.INVITE_ROLE_INVALID),
        )

    # Prevent to invite admin to super_admin or other admins
    if invite_role.id <= deps.current_user_role.id:
        logger.info(
            create_info_logging_message(
                endpoint="/invite",
                feature="user",
                message=str(UserResponses.INVITE_ROLE_INVALID["message"]),
            )
        )
        return JSONResponse(
            status_code=401,
            content=APIResponse(**UserResponses.INVITE_ROLE_INVALID),
        )

    try:

        # Verify if email exists in system
        user = crud.user.get_by_email(db=db, email=request_data.email)
        if user:
            logger.info(
                create_info_logging_message(
                    endpoint="/invite",
                    feature="user",
                    message=str(UserResponses.CREATE_ACCOUNT_EXISTS["message"]),
                )
            )
            return JSONResponse(
                status_code=200,
                content=APIResponse(
                    **UserResponses.CREATE_ACCOUNT_EXISTS, data={"email": request_data.email}
                ),
            )
        # Check if current user is an organization admin
        current_user_role = crud.role.get(db=db, id=current_user.role_id).name

        # Create invited users in User table
        user_in_data = {
            "email": request_data.email,
            "name": request_data.name,
            "role_id": request_data.role_id,
            "hashed_password": get_password_hash(request_data.password),
        }

        # If current user is an organization admin, assign the new user to the same organization
        if current_user_role == AvailableRoles.admin and current_user.organization_id:
            # Get the organization
            organization = db.query(models.Organization).filter(models.Organization.id == current_user.organization_id).first()

            # Check if organization is approved
            if organization and organization.status != OrganizationStatus.APPROVED:
                logger.info(
                    create_info_logging_message(
                        endpoint="/invite",
                        feature="user",
                        message=f"Organization {organization.name} is not approved. Cannot create users.",
                    )
                )
                return JSONResponse(
                    status_code=403,
                    content=APIResponse(
                        success=False,
                        message="Your organization is not approved. Cannot create users.",
                    ),
                )

            # Add organization details to the user
            user_in_data["organization_id"] = current_user.organization_id
            user_in_data["organization_name"] = organization.name if organization else ""

            # Generate an IC for the user based on organization
            user_in_data["IC"] = f"ORG-{current_user.organization_id}-{request_data.email.split('@')[0]}"

        user_in = schemas.UserInDB(**user_in_data)
        user_out = crud.user.create(db=db, obj_in=user_in)
        # sending an email
        background_tasks.add_task(
            send_invitation_email(email_client=email_client, email=request_data.email, password=request_data.password)
        )

        logger.info(
            create_info_logging_message(
                endpoint="/invite",
                feature="user",
                message=str(UserResponses.CREATE_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponses.CREATE_SUCCESS,
                data={
                    "emails": request_data.email,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**UserResponses.INVITATION_UNSUCCESSFUL),
        )


@router.post(
    "/invite-multiple-users",
    status_code=status.HTTP_200_OK,
    response_model=List[schemas.UserOut],
)
async def create_multiple_users_by_file(
    users_file: UploadFile = File(...),
    *,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    email_client: EmailClient = Depends(deps.get_email_client),
    # current_user: models.User = Security(
    #     deps.get_current_user, scopes=[UserScopes.invite]
    # ),
) -> JSONResponse:
    """Invite multiple users through CSV file

    Args:\n
        request_data


    """
    # if not check_user_role(current_user, "admin"):
    #     logger.info(
    #             create_info_logging_message(
    #                 endpoint="/invite-multiple-users",
    #                 feature="user",
    #                 message=str(GeneralResponses.NO_ACCESS["message"]),
    #             )
    #         )
    #     return JSONResponse(
    #         status_code=400,
    #         content=APIResponse(
    #             **GeneralResponses.NO_ACCESS, data={}
    #         ),
    #     )
    file = await users_file.read()
    user_data, cohort_name = check_file_type_and_content(users_file.content_type, file)
    if not user_data or not cohort_name:
        logger.info(
                create_info_logging_message(
                    endpoint="/invite-multiple-users",
                    feature="user",
                    message=str(UserResponses.INCORRECT_FILE_TYPE["message"]),
                )
            )
        return JSONResponse(
            status_code=400,
            content=APIResponse(
                **UserResponses.INCORRECT_FILE_TYPE, data={}
            ),
        )

    try:
        successful_emails = []
        failed_emails = []
        file_hash = calculate_file_hash(file)
        cohort = crud.cohort.get_by_file_hash(db, file_hash)
        if cohort:
            return JSONResponse(
                status_code=200,
                content=APIResponse(
                    **UserResponses.COHORT_ALREADY_EXISTS,
                    data={
                        "cohort_id": cohort.id,
                    },
                ),
            )
        else:
            cohort_in = schemas.CohortCreate(
                name=cohort_name if cohort_name else None,
                file_hash=file_hash,
            )
            cohort = crud.cohort.create(db=db, obj_in=cohort_in)

            if not cohort_name:
                updated_name = f"Cohort {cohort.id}"
                cohort = crud.cohort.update(db=db, db_obj=cohort, obj_in={"name": updated_name})
        db_role = crud.role.get_by_name(db=db, name="learner")
        db_users_list = []
        for each_user in user_data:
            try:
                if not each_user["Email address"] or not is_valid_email(each_user["Email address"]):
                    failed_emails.append(
                        {"email": each_user["Email address"], "message": "Invalid Email"}
                    )
                    logger.info(
                        create_info_logging_message(
                            endpoint="/invite-multiple-users",
                            feature="user",
                            message=f"Invalid email {each_user['Email address']}",
                        )
                    )
                elif crud.user.get_by_email(db=db, email=each_user["Email address"]):
                    failed_emails.append(
                        {"email": each_user["Email address"], "message": "User with email already exists"}
                    )
                    logger.info(
                        create_info_logging_message(
                            endpoint="/invite-multiple-users",
                            feature="user",
                            message=str(UserResponses.CREATE_ACCOUNT_EXISTS["message"]),
                        )
                    )
                else:
                    # Create invited users in User table
                    # password = each_user["password"] if each_user.get("password") else generate_password(length=8)
                    password = "skillseed@01"
                    # d = str(each_user["Date of Birth"])
                    user_in = schemas.UserInDB(
                        name=each_user["First Name"],
                        last_name=each_user.get("Last Name", ""),
                        email=each_user["Email address"],
                        date_of_birth=each_user.get("Date of Birth"),
                        ethnicity=each_user.get("Race", ""),
                        organization_name=each_user.get("Organization Name", ""),
                        role_in_organization=each_user.get("Role in Organization", ""),
                        organization_type=each_user.get("Organization Type", ""),
                        role_id=db_role.id,
                        hashed_password=get_password_hash(password),
                        cohort_id=cohort.id
                    )
                    print(f"Password of user {user_in.email} is {password}")
                    # user_out = crud.user.create(db=db, obj_in=user_in)
                    db_users_list.append(models.User(**user_in.dict()))
                    # sending an email
                    # background_tasks.add_task(
                    #     send_invitation_email(email_client=email_client, email=each_user["email"], password=password)
                    # )
                    successful_emails.append(user_in.email)
                    logger.info(
                        create_info_logging_message(
                            endpoint="/invite-multiple-users",
                            feature="user",
                            message=str(UserResponses.CREATE_SUCCESS["message"]),
                        )
                    )
            except PendingRollbackError:
                db.rollback()
        if db_users_list:
            db.add_all(db_users_list)
            db.commit()
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponses.CREATE_SUCCESS,
                data={
                    "cohort_id": cohort.id,
                    "successful_emails": successful_emails,
                    "failed_emails": failed_emails,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**UserResponses.INVITATION_UNSUCCESSFUL),
        )


@router.patch(
    "/forgot_password",
    status_code=status.HTTP_200_OK,
)
def forgot_password(
    *,
    background_tasks: BackgroundTasks,
    request_data: schemas.ForgotPassword,
    db: Session = Depends(deps.get_db),
    email_client: EmailClient = Depends(deps.get_email_client),
) -> JSONResponse:
    """This will send an Email to user with new random password

    Args:\n
        request_data


    """
    try:

        # Verify if email exists in system
        user = crud.user.get_by_email(db=db, email=request_data.email)
        if not user:
            logger.info(
                create_info_logging_message(
                    endpoint="/forgot_password",
                    feature="user",
                    message=str(UserResponses.USER_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=200,
                content=APIResponse(
                    **UserResponses.USER_NOT_EXIST, data={"email": request_data.email}
                ),
            )

        password = generate_password(length=8)

        # Update password in the DB
        background_tasks.add_task(
            crud.user.update_password_hash(db=db, db_obj=user, hashed_password=get_password_hash(password))
        )
        # sending an email
        background_tasks.add_task(
            send_invitation_email(email_client=email_client, email=request_data.email, password=password)
        )

        logger.info(
            create_info_logging_message(
                endpoint="/forgot_password",
                feature="user",
                message=str(UserResponses.PASSWORD_SENT["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponses.PASSWORD_SENT,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.patch(
    "/change_password",
    status_code=status.HTTP_200_OK,
)
def change_password(
    *,
    background_tasks: BackgroundTasks,
    request_data: schemas.ChangePassword,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[AuthScopes.update_password]
    ),
) -> JSONResponse:
    """Change/Updated password

    Args:\n
        request_data


    """
    try:

        # Verify if email exists in system
        if not verify_password(plain_password=request_data.old_password, hashed_password=current_user.hashed_password):
            logger.info(
                create_info_logging_message(
                    endpoint="/change_password",
                    feature="user",
                    message=str(AuthResponses.PASSWORD_INVALID["message"]),
                )
            )
            return JSONResponse(
                    status_code=200,
                    content=APIResponse(**AuthResponses.PASSWORD_INVALID),
                )

        # Update password in the DB
        background_tasks.add_task(
            crud.user.update_password_hash(db=db, db_obj=current_user, hashed_password=get_password_hash(request_data.new_password))
        )

        logger.info(
            create_info_logging_message(
                endpoint="/change_password",
                feature="user",
                message=str(AuthResponses.PASSWORD_CHANGED_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **AuthResponses.PASSWORD_CHANGED_SUCCESS,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.patch(
    "/update",
    status_code=status.HTTP_200_OK,
    response_model=schemas.UserOut,
)
def user_update(
    *,
    user_update_in: schemas.UserUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.update]
    ),
) -> JSONResponse:
    """Update user data

    Args:\n
        request_data


    """
    try:
        user = crud.user.get(db=db, id=current_user.id)
        # Verify if user exists in system
        if not user:
            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="user",
                    message=str(UserResponses.USER_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                    status_code=404,
                    content=APIResponse(**UserResponses.USER_NOT_EXIST),
                )
        # elif current_user.id == user.id:
        #     logger.info(
        #         create_info_logging_message(
        #             endpoint="/update",
        #             feature="user",
        #             message=str(UserResponses.UPDATE_OWN_ACCOUNT["message"]),
        #         )
        #     )
        #     return JSONResponse(
        #             status_code=400,
        #             content=APIResponse(**UserResponses.UPDATE_OWN_ACCOUNT),
        #         )
        elif user_update_in.email and user_update_in.email != user.email:
            # check if email already exists
            get_user_by_email = crud.user.get_by_email(db=db, email=user_update_in.email)
            if get_user_by_email:
                logger.info(
                    create_info_logging_message(
                        endpoint="/update",
                        feature="user",
                        message=str(UserResponses.CREATE_ACCOUNT_EXISTS["message"]),
                    )
                )
                return JSONResponse(
                    status_code=200,
                    content=APIResponse(
                        **UserResponses.CREATE_ACCOUNT_EXISTS, data={"email": user_update_in.email}
                    ),
                )

        # Check if role is valid or not
        if user_update_in.role_id != None:
            role = crud.role.get(db=db, id=user_update_in.role_id)
            if not role:
                logger.info(
                    create_info_logging_message(
                        endpoint="/update",
                        feature="user",
                        message=str(RoleResponses.INVALID_ROLE["message"]),
                    )
                )
                return JSONResponse(
                    status_code=400,
                    content=APIResponse(**RoleResponses.INVALID_ROLE),
                )

        # Check if role string is valid or not
        if user_update_in.role != None:
            role = crud.role.get_by_name(db=db, name=user_update_in.role)
            if not role:
                logger.info(
                    create_info_logging_message(
                        endpoint="/update",
                        feature="user",
                        message=str(RoleResponses.INVALID_ROLE["message"]),
                    )
                )
                return JSONResponse(
                    status_code=400,
                    content=APIResponse(**RoleResponses.INVALID_ROLE),
                )
            user_update_in.role_id = role.id

        # Check if password updated than generate hash & store it
        # If email changes than generate a new password, calculate hash & store it
        if user_update_in.password:
            password = user_update_in.password
            user_update_in.password = get_password_hash(user_update_in.password)
        elif user_update_in.email:
            password = generate_password(length=8)
            user_update_in.password = get_password_hash(password)

        # Update User in the DB
        background_tasks.add_task(
            crud.user.update(db=db, db_obj=user, obj_in=user_update_in)
        )

        # If password or email changes than send an email to user
        if user_update_in.email or user_update_in.password:
            email = user_update_in.email if user_update_in.email else user.email
            # sending an email
            background_tasks.add_task(
                send_invitation_email(email_client=email_client, email=email, password=password)
            )

        logger.info(
            create_info_logging_message(
                endpoint="/update",
                feature="user",
                message=str(UserResponses.USER_UPDATED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponses.USER_UPDATED,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.patch(
    "/update/{user_id}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.UserOut,
)
def user_update(
    *,
    user_update_in: schemas.UserRoleBase,
    user_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.update]
    ),
) -> JSONResponse:
    """Update user data

    Args:\n
        request_data


    """
    try:
        user = crud.user.get(db=db, id=user_id)
        # Verify if user exists in system
        if not user:
            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="user",
                    message=str(UserResponses.USER_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                    status_code=404,
                    content=APIResponse(**UserResponses.USER_NOT_EXIST),
                )
        elif current_user.id == user.id:
            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="user",
                    message=str(UserResponses.UPDATE_OWN_ACCOUNT["message"]),
                )
            )
            return JSONResponse(
                    status_code=400,
                    content=APIResponse(**UserResponses.UPDATE_OWN_ACCOUNT),
                )
        # elif user_update_in.email and user_update_in.email != user.email:
        #     # check if email already exists
        #     get_user_by_email = crud.user.get_by_email(db=db, email=user_update_in.email)
        #     if get_user_by_email:
        #         logger.info(
        #             create_info_logging_message(
        #                 endpoint="/update",
        #                 feature="user",
        #                 message=str(UserResponses.CREATE_ACCOUNT_EXISTS["message"]),
        #             )
        #         )
        #         return JSONResponse(
        #             status_code=200,
        #             content=APIResponse(
        #                 **UserResponses.CREATE_ACCOUNT_EXISTS, data={"email": user_update_in.email}
        #             ),
        #         )


        # # Check if role is valid or not
        # if user_update_in.role_id != None:
        #     role = crud.role.get(db=db, id=user_update_in.role_id)
        #     if not role:
        #         logger.info(
        #             create_info_logging_message(
        #                 endpoint="/update",
        #                 feature="user",
        #                 message=str(RoleResponses.INVALID_ROLE["message"]),
        #             )
        #         )
        #         return JSONResponse(
        #             status_code=400,
        #             content=APIResponse(**RoleResponses.INVALID_ROLE),
        #         )

        # Check if role string is valid or not
        if user_update_in.role != None:
            role = crud.role.get_by_name(db=db, name=user_update_in.role)
            if not role:
                logger.info(
                    create_info_logging_message(
                        endpoint="/update",
                        feature="user",
                        message=str(RoleResponses.INVALID_ROLE["message"]),
                    )
                )
                return JSONResponse(
                    status_code=400,
                    content=APIResponse(**RoleResponses.INVALID_ROLE),
                )
            user_update_in.role_id = role.id

        # Check if password updated than generate hash & store it
        # If email changes than generate a new password, calculate hash & store it
        # if user_update_in.password:
        #     password = user_update_in.password
        #     user_update_in.password = get_password_hash(user_update_in.password)
        # elif user_update_in.email:
        #     password = generate_password(length=8)
        #     user_update_in.password = get_password_hash(password)

        # Update User in the DB
        background_tasks.add_task(
            crud.user.update(db=db, db_obj=user, obj_in=user_update_in)
        )

        # If password or email changes than send an email to user
        # if user_update_in.email or user_update_in.password:
        #     email = user_update_in.email if user_update_in.email else user.email
        #     # sending an email
        #     background_tasks.add_task(
        #         send_invitation_email(email_client=email_client, email=email, password=password)
        #     )

        logger.info(
            create_info_logging_message(
                endpoint="/update",
                feature="user",
                message=str(UserResponses.USER_UPDATED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponses.USER_UPDATED,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.delete(
    "/delete/{user_id}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.UserOut,
)
def user_delete(
    *,
    user_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.delete]
    ),
) -> JSONResponse:
    """Delete user data

    Args:\n
        request_data


    """
    try:
        user = crud.user.get(db=db, id=user_id)
        # Verify if user exists in system
        if not user:
            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="user",
                    message=str(UserResponses.USER_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(**UserResponses.USER_NOT_EXIST),
            )
        elif current_user.id == user.id:
            # user cannot delete it's own account
            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="user",
                    message=str(UserResponses.DELETE_OWN_ACCOUNT["message"]),
                )
            )
            return JSONResponse(
                status_code=400,
                content=APIResponse(**UserResponses.DELETE_OWN_ACCOUNT),
            )
        elif user.role_id <= current_user.role_id:
            # Prevent user to delete account of same role
            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="user",
                    message=str(UserResponses.DELETE_FAILED["message"]),
                )
            )
            return JSONResponse(
                status_code=400,
                content=APIResponse(**UserResponses.DELETE_FAILED),
            )

        # Update User in the DB
        background_tasks.add_task(
            crud.user.remove(db=db, id=user_id)
        )

        logger.info(
            create_info_logging_message(
                endpoint="/delete",
                feature="user",
                message=str(UserResponses.USER_DELETED_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponses.USER_DELETED_SUCCESS,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/get",
    status_code=status.HTTP_200_OK,
    response_model=schemas.UserInviteOut,
)
def get_user(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.read]
    ),
) -> JSONResponse:
    """Get current user data

    Args:\n
        request_data


    """
    try:
        # Verify if user exists in system
        return current_user
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/get/{user_id}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.UserOut,
)
def get_user_by_id(
    user_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.read]
    ),
) -> JSONResponse:
    """Get user details by ID

    Args:\n
        user_id: The ID of the user to retrieve

    Returns:
        User details
    """
    try:
        # Get user by ID
        user = crud.user.get(db=db, id=user_id)

        # Check if user exists
        if not user:
            logger.info(
                create_info_logging_message(
                    endpoint="/get/{user_id}",
                    feature="user",
                    message=str(UserResponses.USER_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(**UserResponses.USER_NOT_EXIST),
            )

        # Check if current user has permission to view this user
        # Users can only view users with role_id greater than their own (lower privilege)
        if user.role_id <= current_user.role_id and user.id != current_user.id:
            logger.info(
                create_info_logging_message(
                    endpoint="/get/{user_id}",
                    feature="user",
                    message=str(GeneralResponses.NO_ACCESS["message"]),
                )
            )
            return JSONResponse(
                status_code=403,
                content=APIResponse(**GeneralResponses.NO_ACCESS),
            )

        # Get role name
        role = crud.role.get(db=db, id=user.role_id)
        role_name = role.name if role else None

        # Create user response object
        user_dict = user.to_dict()
        user_dict["role_name"] = role_name

        logger.info(
            create_info_logging_message(
                endpoint="/get/{user_id}",
                feature="user",
                message=str(UserResponses.USER_DETAILS_RETRIEVED["message"]),
            )
        )

        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **UserResponses.USER_DETAILS_RETRIEVED,
                data=user_dict
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

@router.get(
    "/get/all",
    status_code=status.HTTP_200_OK,
    response_model=Page[schemas.UserRoleOut]
)
def user_get_all(
    search: str = "",
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.read]
    ),
) -> JSONResponse:
    """Get all user data

    Args:\n
        request_data


    """
    try:
        if search:
            return paginate(
                db.query(models.User.id, models.User.name, models.User.email, models.User.id, models.Role.name.label("role"))\
                    .join(models.Role, models.User.role_id == models.Role.id)\
                    .filter(current_user.role_id < models.User.role_id)
                    .filter(models.User.email.ilike(f"%{search}%"))
                    .order_by(models.User.id)
            )
        else:
            return paginate(
                db.query(models.User.id, models.User.name, models.User.email, models.User.id, models.Role.name.label("role"))\
                    .join(models.Role, models.User.role_id == models.Role.id)\
                    .filter(current_user.role_id < models.User.role_id)
                    .order_by(models.User.id)
            )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

@router.get(
    "/get-by-cohort/{cohort_id}",
    status_code=status.HTTP_200_OK,
    response_model=Page[schemas.UserOut]
)
def get_users_by_cohort_id(
    cohort_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.read]
    ),
) -> JSONResponse:
    """Get user data by cohort id

    Args:\n
        request_data


    """
    try:
        learner_role = crud.role.get_by_name(db=db, name="learner")
        return paginate(
            db.query(models.User)\
                .filter(models.User.cohort_id == cohort_id)
                .filter(models.User.role_id == learner_role.id)
                .order_by(models.User.id)
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/search", response_model=List[schemas.UserSearchResponse])
def search_user_by_email_partial(
    *,
    query: str,
    db: Session = Depends(deps.get_db)):
    try:
        learner_role = crud.role.get_by_name(db=db, name="learner")
        return db.query(models.User).filter(models.User.role_id == learner_role.id).filter(models.User.email.ilike(f"%{query}%")).limit(10).all()
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
