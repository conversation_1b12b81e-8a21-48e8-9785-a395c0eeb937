"""
This file contains all the API's related to HeygenVideo
"""

import os
import json
from typing import List
from datetime import datetime
from dotenv import load_dotenv

from fastapi import APIRouter, Depends, File, Form, Security, UploadFile, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.scopes import HeygenVideoScopes
from app.utils.file_handler import upload_file_to_s3, generate_presigned_url
from app.logger import logger
from app.utils.globals import create_info_logging_message

load_dotenv()
AWS_BUCKET_NAME = os.getenv('AWS_BUCKET_NAME')

# Custom JSON encoder to handle datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super(DateTimeEncoder, self).default(obj)


router = APIRouter()


@router.post("/create", response_model=schemas.HeygenVideo)
async def create_heygen_video(
    *,
    name: str = Form(...),
    video_file: UploadFile = File(...),
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[HeygenVideoScopes.create]
    ),
) -> JSONResponse:
    """Create a new Heygen video

    Args:
        name (str): Name of the video
        video_file (UploadFile): Video file
        db (Session): Database session
        current_user (models.User): Current user

    Returns:
        JSONResponse: Created Heygen video
    """
    try:
        # Check if user is an instructor
        if current_user.role_id != 3:  # Instructor role_id is 3
            raise APIException(
                status_code=status.HTTP_403_FORBIDDEN,
                message={
                    "success": False,
                    "message": "Only instructors can create Heygen videos"
                }
            )

        # Create folder path for the video
        folder_path = f"heygen_videos/{current_user.id}"

        # Upload video to S3 and get a presigned URL
        s3_path, presigned_url = upload_file_to_s3(folder=folder_path, file=video_file, make_public=True)

        # Create a new HeygenVideo object directly
        # Note: We're not storing the presigned URL in the database since it expires
        # Instead, we'll generate a fresh one each time the video is accessed
        heygen_video = models.HeygenVideo(
            name=name,
            instructor_id=current_user.id,
            s3_path=s3_path,
            public_url=None  # No longer storing public URLs
        )

        # Add to database
        db.add(heygen_video)
        db.commit()
        db.refresh(heygen_video)

        # Return HeygenVideo data
        heygen_video_dict = schemas.HeygenVideo.from_orm(heygen_video)
        heygen_video_out = jsonable_encoder(heygen_video_dict)

        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="heygen_video",
                message=f"Heygen video '{name}' created successfully by instructor {current_user.id}"
            )
        )

        # Use custom JSON encoder to handle datetime objects
        response_data = APIResponse(
            success=True,
            message="Heygen video created successfully",
            data=heygen_video_out
        )

        # Convert to JSON string with custom encoder, then parse back to dict
        json_str = json.dumps(response_data, cls=DateTimeEncoder)
        json_dict = json.loads(json_str)

        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content=json_dict
        )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message={
                "success": False,
                "message": f"Failed to create Heygen video: {str(e)}"
            },
        )


@router.get("/list", response_model=List[schemas.HeygenVideoWithUrl])
async def list_heygen_videos(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[HeygenVideoScopes.read]
    ),
) -> JSONResponse:
    """List all Heygen videos for the current instructor

    Args:
        db (Session): Database session
        current_user (models.User): Current user

    Returns:
        JSONResponse: List of Heygen videos
    """
    try:
        # Check if user is an instructor
        if current_user.role_id != 3:  # Instructor role_id is 3
            raise APIException(
                status_code=status.HTTP_403_FORBIDDEN,
                message={
                    "success": False,
                    "message": "Only instructors can list Heygen videos"
                }
            )

        # Get all HeygenVideos for the instructor
        heygen_videos = crud.heygen_video.get_by_instructor(db=db, instructor_id=current_user.id)

        # Generate presigned URLs for each video
        heygen_videos_with_url = []
        for video in heygen_videos:
            video_dict = schemas.HeygenVideo.from_orm(video).dict()
            # Always generate a fresh presigned URL
            video_dict["video_url"] = generate_presigned_url(video.s3_path)
            heygen_videos_with_url.append(video_dict)

        logger.info(
            create_info_logging_message(
                endpoint="/list",
                feature="heygen_video",
                message=f"Listed {len(heygen_videos)} Heygen videos for instructor {current_user.id}"
            )
        )

        # Use custom JSON encoder to handle datetime objects
        response_data = APIResponse(
            success=True,
            message="Heygen videos retrieved successfully",
            data=heygen_videos_with_url
        )

        # Convert to JSON string with custom encoder, then parse back to dict
        json_str = json.dumps(response_data, cls=DateTimeEncoder)
        json_dict = json.loads(json_str)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=json_dict
        )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message={
                "success": False,
                "message": f"Failed to list Heygen videos: {str(e)}"
            },
        )


@router.get("/{heygen_video_id}", response_model=schemas.HeygenVideoWithUrl)
async def get_heygen_video(
    *,
    heygen_video_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[HeygenVideoScopes.read]
    ),
) -> JSONResponse:
    """Get a specific Heygen video

    Args:
        heygen_video_id (int): ID of the Heygen video
        db (Session): Database session
        current_user (models.User): Current user

    Returns:
        JSONResponse: Heygen video details
    """
    try:
        # Get the HeygenVideo
        heygen_video = crud.heygen_video.get(db=db, id=heygen_video_id)

        if not heygen_video:
            raise APIException(
                status_code=status.HTTP_404_NOT_FOUND,
                message={
                    "success": False,
                    "message": "Heygen video not found"
                }
            )

        # Check if the video belongs to the current instructor
        # if heygen_video.instructor_id != current_user.id:
        #     raise APIException(
        #         status_code=status.HTTP_403_FORBIDDEN,
        #         message={
        #             "success": False,
        #             "message": "You don't have permission to access this Heygen video"
        #         }
        #     )

        # Generate presigned URL for the video
        video_dict = schemas.HeygenVideo.from_orm(heygen_video).dict()
        # Always generate a fresh presigned URL
        video_dict["video_url"] = generate_presigned_url(heygen_video.s3_path)

        logger.info(
            create_info_logging_message(
                endpoint="/{heygen_video_id}",
                feature="heygen_video",
                message=f"Retrieved Heygen video {heygen_video_id} for instructor {current_user.id}"
            )
        )

        # Use custom JSON encoder to handle datetime objects
        response_data = APIResponse(
            success=True,
            message="Heygen video retrieved successfully",
            data=video_dict
        )

        # Convert to JSON string with custom encoder, then parse back to dict
        json_str = json.dumps(response_data, cls=DateTimeEncoder)
        json_dict = json.loads(json_str)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=json_dict
        )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message={
                "success": False,
                "message": f"Failed to get Heygen video: {str(e)}"
            },
        )
