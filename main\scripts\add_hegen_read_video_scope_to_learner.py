
import os
import sys
from pathlib import Path

# Add the parent directory to Python path
current_dir = Path(__file__).resolve().parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

from sqlalchemy.orm import Session
from app import crud, schemas
from app.db.session import <PERSON>Local
from app.scopes.heygen_video import HeygenVideoScopes

def add_heygen_permission():
    db = SessionLocal()
    try:
        # Get the learner role
        learner_role = crud.role.get_by_name(db=db, name="learner")

        # Get Permission id
        permission = crud.permission.get_by_name(db=db, name=HeygenVideoScopes.read)
        
        # Assign permission to learner role
        role_permission_in = schemas.RolePermissionCreate(
            role_id=learner_role.id,
            permission_id=permission.id
        )
        crud.role_permission.create(db=db, obj_in=role_permission_in)
        
        print("Successfully added HeyGen read video permission to learner role")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    add_heygen_permission()