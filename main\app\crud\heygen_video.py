"""
This file contains all the CRUD operations
for HeygenVideo model
"""

from typing import List, Optional

from app.models import HeygenVideo
from app.schemas.heygen_video import HeygenVideoCreate, HeygenVideoUpdate

from sqlalchemy.orm import Session


def get(db: Session, id: int) -> Optional[HeygenVideo]:
    """Get a HeygenVideo by ID

    Args:
        db (Session): SQLAlchemy Session
        id (int): HeygenVideo ID

    Returns:
        Optional[HeygenVideo]: HeygenVideo object or None
    """
    return db.query(HeygenVideo).filter(HeygenVideo.id == id).first()


def get_by_instructor(db: Session, instructor_id: int) -> List[HeygenVideo]:
    """Get all HeygenVideos for an instructor

    Args:
        db (Session): SQLAlchemy Session
        instructor_id (int): Instructor ID

    Returns:
        List[HeygenVideo]: List of HeygenVideo objects
    """
    return db.query(HeygenVideo).filter(HeygenVideo.instructor_id == instructor_id).all()


def create(db: Session, *, obj_in: HeygenVideoCreate, instructor_id: int, s3_path: str) -> HeygenVideo:
    """Create a new HeygenVideo

    Args:
        db (Session): SQLAlchemy Session
        obj_in (HeygenVideoCreate): HeygenVideo data
        instructor_id (int): Instructor ID
        s3_path (str): S3 path of the video

    Returns:
        HeygenVideo: Created HeygenVideo
    """
    db_obj = HeygenVideo(
        name=obj_in.name,
        instructor_id=instructor_id,
        s3_path=s3_path
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(db: Session, *, db_obj: HeygenVideo, obj_in: HeygenVideoUpdate) -> HeygenVideo:
    """Update a HeygenVideo

    Args:
        db (Session): SQLAlchemy Session
        db_obj (HeygenVideo): HeygenVideo to update
        obj_in (HeygenVideoUpdate): HeygenVideo data

    Returns:
        HeygenVideo: Updated HeygenVideo
    """
    if obj_in.name is not None:
        db_obj.name = obj_in.name
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> HeygenVideo:
    """Delete a HeygenVideo

    Args:
        db (Session): SQLAlchemy Session
        id (int): HeygenVideo ID

    Returns:
        HeygenVideo: Deleted HeygenVideo
    """
    obj = db.query(HeygenVideo).get(id)
    db.delete(obj)
    db.commit()
    return obj
