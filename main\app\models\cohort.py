"""
This file contains the model mapping of 
role table
"""

from datetime import datetime

from app.db.base_class import Base

from sqlalchemy import Column, DateTime, String


class Cohort(Base):
    name = Column(
        String(40), comment="cohort name"
    )
    file_hash = Column(
        String(100), unique=True, comment="user name"
    )
    file_path = Column(
        String(200), comment="user file path"
    )
    created_on = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
