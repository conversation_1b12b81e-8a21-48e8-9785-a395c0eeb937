"""
This file contains all the API's related to 
level roles 
"""

from app import crud, models, schemas
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.responses import RoleResponses, GeneralResponses
from app.scopes import RoleScopes
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi.encoders import jsonable_encoder
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate

router = APIRouter()


@router.post("/create", response_model=schemas.Role)
def create_role(
    *,
    role_in: schemas.RoleCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RoleScopes.create]
    ),
) -> JSONResponse:
    """Create new role

    Args:
        role_in (schemas.RoleCreate): RoleCreate Pydantic Model Role to create
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing current role
    """

    try:
        # Get payload & create role
        role = crud.role.create(db=db, obj_in=role_in)
        # Return a Pydantic Validated Permission Model
        role_dict = role.to_dict()
        role_dict = schemas.Role(**role_dict)
        role_out = jsonable_encoder(role_dict)

        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="role",
                message=str(RoleResponses.ROLE_CREATE_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content=APIResponse(**RoleResponses.ROLE_CREATE_SUCCESS, data=role_out),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.patch("/update/{role_id}", response_model=schemas.Role)
def update_role(
    *,
    role_id: int,
    role_in: schemas.RoleUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RoleScopes.update]
    ),
) -> JSONResponse:
    """Update exiting role

    Args:
        role_in (schemas.RoleUpdate): RoleUpdate Pydantic Model Role to update
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing current role
    """

    try:
        # Check if role exists or not
        get_existing_role = crud.role.get_by_id(db=db, id=role_id)

        if get_existing_role:
            # update role if exists
            role = crud.role.update(db=db, db_obj=get_existing_role, obj_in=role_in)
            # Return a Pydantic Validated Permission Model
            role_dict = role.to_dict()
            role_dict = schemas.Role(**role_dict)
            role_out = jsonable_encoder(role_dict)

            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="role",
                    message=str(RoleResponses.ROLE_UPDATE_SUCCESS["message"]),
                )
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=APIResponse(**RoleResponses.ROLE_UPDATE_SUCCESS, data=role_out),
            )
        logger.info(
            create_info_logging_message(
                endpoint="/update",
                feature="role",
                message=str(GeneralResponses.DATA_DOES_NOT_EXITS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(**GeneralResponses.DATA_DOES_NOT_EXITS),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.delete("/delete/{role_id}", response_model=schemas.Role)
def delete_role(
    *,
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RoleScopes.delete]
    ),
) -> JSONResponse:
    """Delete exiting role

    Args:
        payload_delete_role (schemas.RoleDelete): RoleDelete Pydantic Model Role to update
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing response message
    """

    try:
        # Check if role exists or not
        get_role = crud.role.get_by_id(db=db, id=role_id)

        if get_role:
            # delete role, let cascade delete handle the rest
            # role cannot be deleted if it is assigned to any user (RESTRICT clause in DB)
            role = crud.role.remove(db=db, id=get_role.id)

            # Return a Pydantic Validated Permission Model
            role_dict = role.to_dict()
            role_dict = schemas.Role(**role_dict)
            role_out = jsonable_encoder(role_dict)

            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="role",
                    message=str(RoleResponses.ROLE_DELETE_SUCCESS["message"]),
                )
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=APIResponse(**RoleResponses.ROLE_DELETE_SUCCESS, data=role_out),
            )
        logger.info(
            create_info_logging_message(
                endpoint="/delete",
                feature="role",
                message=str(GeneralResponses.DATA_DOES_NOT_EXITS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(**GeneralResponses.DATA_DOES_NOT_EXITS),
        )
    except IntegrityError as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_DB_EXCEPTION),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/get/all", response_model=Page[schemas.Role])
def all_roles(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RoleScopes.read]
    ),
) -> JSONResponse:
    """Fetch all roles

    Args:
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing all roles
    """

    try:
        # Get current user role
        current_user_role = crud.role.get(db=db, id=current_user.role_id)
        return paginate(
            db.query(models.Role).filter(current_user_role.id < models.Role.id)
        )

    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get("/get/{role_id}")
def get_role(
    *,
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[RoleScopes.read]
    ),
) -> JSONResponse:
    """Fetch a single role

    Args:
        role_id (int): Role ID
        db (Session): SQLAlchemy Session. Defaults to Depends(deps.get_db).
        current_user (User): Current User. Defaults to Security(deps.get_current_user).

    Returns:
        JSONResponse: JSONResponse containing role
    """

    try:
        # Check if role exists or not
        get_role = crud.role.get_by_id(db=db, id=role_id)

        if get_role:
            # Return a Pydantic Validated Permission Model
            role_dict = get_role.to_dict()
            role_dict = schemas.Role(**role_dict)
            role_out = jsonable_encoder(role_dict)

            logger.info(
                create_info_logging_message(
                    endpoint="/get",
                    feature="role",
                    message=str(RoleResponses.ROLE_GET_SUCCESS["message"]),
                )
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=APIResponse(**RoleResponses.ROLE_GET_SUCCESS, data=role_out),
            )
        logger.info(
            create_info_logging_message(
                endpoint="/get",
                feature="role",
                message=str(GeneralResponses.DATA_DOES_NOT_EXITS["message"]),
            )
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=APIResponse(**GeneralResponses.DATA_DOES_NOT_EXITS),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )
