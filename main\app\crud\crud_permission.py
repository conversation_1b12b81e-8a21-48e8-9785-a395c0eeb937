"""
This file contains all the crud operations for 
permission feature
"""

from typing import Optional

from app.crud.base import CRUDBase
from app.models.permission import Permission
from app.schemas.permission import PermissionCreate, PermissionUpdate

from sqlalchemy.orm import Session


class CRUDPermission(CRUDBase[Permission, PermissionCreate, PermissionUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Permission]:
        return db.query(Permission).filter(Permission.name == name).first()

    def get_by_id(self, db: Session, *, id: int) -> Optional[Permission]:
        return db.query(Permission).filter(Permission.id == id).first()

    def get_all_permissions(self, db: Session) -> list[Permission]:
        return db.query(Permission).all()


permission = CRUDPermission(Permission)
