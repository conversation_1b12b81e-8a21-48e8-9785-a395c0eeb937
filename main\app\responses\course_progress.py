from pydantic import BaseModel


class CourseProgressResponsesClass(BaseModel):

    COURSE_DELETED_SUCCESS: dict = {
        "success": True,
        "message": "Course Progress deleted successfully.",
    }
    
    COURSE_UPDATED: dict = {
        "success": True,
        "message": "Course Progress updated successfully.",
    }

    DELETE_FAILED: dict = {
        "success": False,
        "message": "Failed to delete course.",
    }

    SUCCESSFULLY_ENROLLED: dict = {
        "success": True,
        "message": "User is successfully enrolled in course.",
    }

    ALREADY_ENROLLED: dict = {
        "success": False,
        "message": "User is already enrolled in course.",
    }

CourseProgressResponses = CourseProgressResponsesClass()
