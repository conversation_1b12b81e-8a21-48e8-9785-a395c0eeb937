"""
This file contains all the API's related to course
"""

import random

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.services import EmailClient
from app.core.security import get_password_hash, verify_password
from app.scopes import CourseScopes, AuthScopes
from app.responses import CourseResponses, GeneralResponses, AuthResponses, RoleResponses
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from typing import List

router = APIRouter()


def update_course(db: Session, db_course: models.Course, course_in: schemas.CourseUpdate):
    if course_in.name:
        crud.course.update(db=db, db_obj=db_course, obj_in={"name": course_in.name})
    if course_in.activities:
        db.query(models.CourseActivityAssociation).filter(models.CourseActivityAssociation.course_id == 
                                                          db_course.id).delete()
        db.add_all([models.CourseActivityAssociation(course_id=db_course.id, activity_id=a_id, sequence=idx) 
                    for idx, a_id in enumerate(course_in.activities)])
        db.commit()


@router.post(
    "/create",
    status_code=status.HTTP_200_OK,
    response_model=schemas.CourseReturn
)
def create_course(
    *,
    # background_tasks: BackgroundTasks,
    request_data: schemas.CourseCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseScopes.create]
    ),
) -> JSONResponse:
    """Create a new course

    Args:\n
        request_data


    """
    
    try:
        # Create course in Course table
        course_in = schemas.CourseInDB(
            name=request_data.name,
            activities=request_data.activities,
            instructor=current_user.id,
            # role_id=request_data.role_id,
        )
        course_out = crud.course.create(db=db, obj_in=course_in)
        
        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="course",
                message=str(CourseResponses.CREATE_SUCCESS["message"] + " " + str(current_user.id)),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **CourseResponses.CREATE_SUCCESS,
                data={
                    "name": request_data.name,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**CourseResponses.CREATE_FAILED),
        )


@router.patch(
    "/update/{course_id}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.CourseReturn
)
def course_update(
    *,
    course_id: int,
    course_update_in: schemas.CourseUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    # email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseScopes.update]
    ),
) -> JSONResponse:
    """Update user data

    Args:\n
        request_data


    """
    try:
        course = crud.course.get(db=db, id=course_id)
        # Verify if user exists in system
        if not course:
            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="course",
                    message=str(CourseResponses.COURSE_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                    status_code=404,
                    content=APIResponse(**CourseResponses.COURSE_NOT_EXIST),
                )
            
        # Update User in the DB
        background_tasks.add_task(
            update_course(db=db, db_course=course, course_in=course_update_in)
        )
        
        logger.info(
            create_info_logging_message(
                endpoint="/update",
                feature="course",
                message=str(CourseResponses.COURSE_UPDATED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **CourseResponses.COURSE_UPDATED,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.delete(
    "/delete/{course_id}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.CourseReturn
)
def course_delete(
    *,
    course_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseScopes.delete]
    ),
) -> JSONResponse:
    """Delete course data

    Args:\n
        request_data


    """
    try:
        course = crud.course.get(db=db, id=course_id)
        # Verify if user exists in system
        if not course:
            logger.info(
                create_info_logging_message(
                    endpoint="/delete",
                    feature="course",
                    message=str(CourseResponses.COURSE_NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                status_code=404,
                content=APIResponse(**CourseResponses.COURSE_NOT_EXIST),
            )
        
        # Update User in the DB
        # First delete associated course_progress records
        crud.course_progress.remove_by_course(db=db, course_id=course_id)
        # Then delete the course
        background_tasks.add_task(crud.course.remove(db=db, id=course_id))
        
        logger.info(
            create_info_logging_message(
                endpoint="/delete",
                feature="course",
                message=str(CourseResponses.COURSE_DELETED_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **CourseResponses.COURSE_DELETED_SUCCESS,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

@router.get(
    "/get/all",
    status_code=status.HTTP_200_OK,
    response_model=List[schemas.CourseDetailReturn]
)
def get_all_courses(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseScopes.read]
    ),
) -> JSONResponse:
    """Get all courses

    Args:\n
        request_data


    """
    try:
        courses = []
        db_courses = db.query(models.Course).all()
        for course in db_courses:
            course_dict = course.__dict__
            activities = []
            db_activities = db.query(models.CourseActivityAssociation).filter(models.CourseActivityAssociation.course_id ==
                                                                                course.id).all()
            for csa in db_activities:
                activity = crud.activity.get(db=db, id=csa.activity_id)
                activities.append({
                    'id': activity.id,
                    'name': activity.name,
                    'type': activity.type,
                    'sequence': csa.sequence,
                })
            course_dict['activities'] = sorted(activities, key=lambda x: x["sequence"])
            courses.append(course_dict)
        return courses
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

@router.get(
    "/get/{course_id:int}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.CourseDetailReturn
)
def get_all_courses(
    course_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CourseScopes.read]
    ),
) -> JSONResponse:
    """Get all courses

    Args:\n
        request_data


    """
    try:
        course = crud.course.get(db=db, id=course_id).__dict__
        activities = []
        db_activities = db.query(models.CourseActivityAssociation).filter(models.CourseActivityAssociation.course_id ==
                                                                            course_id).all()
        for csa in db_activities:
            activity = crud.activity.get(db=db, id=csa.activity_id)
            activities.append({
                'id': activity.id,
                'name': activity.name,
                'type': activity.type,
                'sequence': csa.sequence,
            })
        course['activities'] = sorted(activities, key=lambda x: x["sequence"])
        return course
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

