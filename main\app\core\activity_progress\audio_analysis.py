import base64
import os
import logging
from openai import OpenAI
from dotenv import load_dotenv
import subprocess
import numpy as np
import matplotlib.pyplot as plt
import noisereduce as nr
import librosa
from .constants import base_prompt
from .constants_all_modalities import base_prompt_v2, base_prompt_v3, BASE_PROMPT_RUBRIC_EVAL
import json
import gc
import time
import tempfile
import cv2
from pydub import AudioSegment
from copy import deepcopy
from app.utils.decorators import memory_usage
from contextlib import contextmanager
import asyncio
from .rubric_prompt_generation import generate_dynamic_evaluation_prompt, SAMPLE_RESPONSE_STRUCT

# Configure logging
logger = logging.getLogger(__name__)

# Load the .env file
load_dotenv()

# Get the API key
api_key = os.getenv('OUR_OPENAI_API_KEY')

# Initialize OpenAI client once
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY", api_key))

# JSON response structures
response_struct_res = json.dumps({"responding": {"facial_expressions": 4, "eye_contact": 5, "body_movement_and_posture": 4, "gestures": 4, "tone_and_manner_of_speech": 5, "choice_of_words": 5}, "you_did_well_at_the_following": ["", "", ""], "you_can_improve_by_focusing_on_the_following": ["", ""]})

response_struct_demo = json.dumps({"facialExpressions": {"calmAndApproachableExpression": "Yes/No", "engagedListening": "Yes/No", "noSignsOfFrustrationOrAnnoyance": "Yes/No", "supportiveGestures": "Yes/No", "openAndRelaxedFacialFeatures": "Yes/No"}, "soundToneOfVoice": {"calmAndSteadyTone": "Yes/No", "empatheticTone": "Yes/No", "clearArticulation": "Yes/No", "nonDefensiveTone": "Yes/No", "appropriateVolume": "Yes/No"}, "textChoiceOfWords": {"acknowledgmentOfTheIssue": "Yes/No", "useOfPoliteLanguage": "Yes/No", "solutionOrientedWords": "Yes/No", "apologeticAndResponsibleLanguage": "Yes/No", "avoidanceOfBlamingLanguage": "Yes/No"}, "overallScore": "integer", "feedback": {"positiveAreas": "<summary of areas where the person got high scores>", "improvementSuggestions": "<suggestions based on areas where the person got low scores>"}})


@contextmanager
def temp_file(suffix=None):
    """Context manager for temporary files that ensures cleanup."""
    # Create temp file in a directory that's definitely writable in Docker
    temp_dir = "/tmp"
    if not os.path.exists(temp_dir) or not os.access(temp_dir, os.W_OK):
        temp_dir = os.getcwd()  # Fallback to current directory
        
    # Create a named temporary file that persists after closing
    fd, temp_path = tempfile.mkstemp(suffix=suffix, dir=temp_dir)
    os.close(fd)  # Close the file descriptor but keep the file
    
    try:
        yield temp_path
    finally:
        try:
            if os.path.exists(temp_path):
                os.remove(temp_path)
        except OSError as e:
            logger.error(f"Error removing temporary file {temp_path}: {e}")


@memory_usage
def extract_audio_from_video(video_path):
    """Extract audio from video using ffmpeg."""
    try:
        # Create a temporary file path for the audio output
        audio_output_path = tempfile.mktemp(suffix=".wav")
        
        # Run ffmpeg command to extract audio
        command = f"ffmpeg -i \"{video_path}\" -q:a 0 -map a \"{audio_output_path}\" -y"
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"FFmpeg error: {result.stderr}")
            raise RuntimeError(f"Failed to extract audio: {result.stderr}")
            
        # Verify the file exists after extraction
        if not os.path.exists(audio_output_path):
            raise FileNotFoundError(f"Audio file not created at {audio_output_path}")
            
        logger.info(f"Audio extracted to {audio_output_path}")
        
        # Read the audio data into memory before the file is deleted
        audio_data = open(audio_output_path, "rb").read()
        
        # Create a new temporary file that will persist
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_file.write(audio_data)
            persistent_path = temp_file.name
            
        logger.info(f"Audio saved to persistent path: {persistent_path}")
        return persistent_path
    except Exception as e:
        logger.exception(f"Error extracting audio: {str(e)}")
        raise


def is_audio_empty(audio_path, silence_threshold=-50.0):
    """Check if audio file contains meaningful sound."""
    try:
        # Verify the file exists before attempting to open it
        if not os.path.exists(audio_path):
            logger.error(f"Audio file not found at {audio_path}")
            raise FileNotFoundError(f"Audio file not found at {audio_path}")
            
        audio = AudioSegment.from_wav(audio_path)
        loudness = audio.dBFS
        return loudness < silence_threshold
    except Exception as e:
        logger.exception(f"Error checking audio: {str(e)}")
        raise


@memory_usage
def save_spectrogram(audio_path):
    """Generate and save audio spectrogram with reduced memory usage."""
    try:
        # Use non-interactive Agg backend which is thread-safe
        import matplotlib
        matplotlib.use('Agg')  # Must be called before importing pyplot
        
        # Use lower sample rate to reduce memory
        y, sr = librosa.load(audio_path, sr=16000)
        
        # Apply noise reduction
        reduced_noise = nr.reduce_noise(y=y, sr=sr)
        
        # Free memory
        del y
        gc.collect()
        
        # Generate mel spectrogram with fewer mel bands
        S = librosa.feature.melspectrogram(y=reduced_noise, sr=sr, n_mels=64)
        S_dB = librosa.power_to_db(S, ref=np.max)
        
        # Free memory
        del reduced_noise, S
        gc.collect()
        
        # Create a direct temporary file without using context manager
        # This ensures the file persists until explicitly deleted
        temp_dir = "/tmp"
        if not os.path.exists(temp_dir) or not os.access(temp_dir, os.W_OK):
            temp_dir = os.getcwd()  # Fallback to current directory
            
        output_path = tempfile.mktemp(suffix=".png", dir=temp_dir)
        
        # Use a smaller figure size and non-interactive figure
        plt.figure(figsize=(8, 3), dpi=100)
        librosa.display.specshow(S_dB, sr=sr, x_axis='time', y_axis='mel')
        plt.colorbar(format='%+2.0f dB')
        plt.title('Mel-frequency spectrogram')
        
        # Save the figure and close it
        plt.savefig(output_path, bbox_inches='tight')
        plt.close('all')  # Explicitly close all figures
        
        # Verify the file exists after saving
        if not os.path.exists(output_path):
            raise FileNotFoundError(f"Spectrogram file not created at {output_path}")
            
        logger.info(f"Spectrogram saved to {output_path}")
        return output_path
    except Exception as e:
        logger.exception(f"Error creating spectrogram: {str(e)}")
        raise


def encode_image(image_path):
    """Encode image to base64 with proper resource management."""
    try:
        # Verify the file exists before attempting to open it
        if not os.path.exists(image_path):
            logger.error(f"Image file not found at {image_path}")
            raise FileNotFoundError(f"Image file not found at {image_path}")
            
        with open(image_path, "rb") as image_file:
            encoded = base64.b64encode(image_file.read()).decode('utf-8')
        
        logger.info(f"Successfully encoded image at {image_path}")
        return encoded
    except Exception as e:
        logger.exception(f"Error encoding image: {str(e)}")
        raise


@memory_usage
def transcribe_audio(input_audio_path):
    """Transcribe audio using OpenAI's Whisper API."""
    try:
        with open(input_audio_path, "rb") as audio_file:
            transcription = client.audio.transcriptions.create(
                model="whisper-1",
                language="en",
                file=audio_file,
                response_format="json"
            )
        return transcription.text
    except Exception as e:
        logger.exception(f"Error transcribing audio: {str(e)}")
        raise


@memory_usage
def read_video(video_path, max_frames=230):
    """Read video frames efficiently with adaptive sampling."""
    try:
        video = cv2.VideoCapture(video_path)
        if not video.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
            
        total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = video.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0
        
        logger.info(f"Video stats: {total_frames} frames, {fps} fps, {duration:.2f}s")
        
        # Adaptive sampling - take more frames from shorter videos
        if duration <= 30:
            frame_interval = max(5, np.ceil(total_frames / max_frames))
        elif duration <= 60:
            frame_interval = max(10, np.ceil(total_frames / max_frames))
        else:
            frame_interval = max(15, np.ceil(total_frames / max_frames))
            
        logger.info(f"Using frame interval: {frame_interval}")
        
        base64Frames = []
        frame_count = 0
        
        # Use JPEG encoding with reduced quality for smaller size
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 80]
        
        while video.isOpened():
            success, frame = video.read()
            if not success or len(base64Frames) >= max_frames:
                break
                
            if frame_count % int(frame_interval) == 0:
                # Resize frame to reduce memory usage
                frame = cv2.resize(frame, (0, 0), fx=0.5, fy=0.5)
                _, buffer = cv2.imencode(".jpg", frame, encode_params)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
                
            frame_count += 1
            
        video.release()
        logger.info(f"Extracted {len(base64Frames)} frames from video")
        return base64Frames
    except Exception as e:
        logger.exception(f"Error reading video: {str(e)}")
        if 'video' in locals() and video.isOpened():
            video.release()
        raise


@memory_usage
def process_single_video(spectrogram_path, transcription_text, base64Frames, version, interlocutor_profile, scenario_description, evaluation_rubric):
    """Process video with OpenAI API, optimized for memory and token usage."""
    try:
        # Verify spectrogram file exists
        if not os.path.exists(spectrogram_path):
            logger.error(f"Spectrogram file not found at {spectrogram_path}")
            raise FileNotFoundError(f"Spectrogram file not found at {spectrogram_path}")
            
        spectrogram_image = encode_image(spectrogram_path)
        logger.info("Spectrogram successfully encoded")
        
        # Select appropriate prompt based on version
        if version == "1":
            prompt = deepcopy(base_prompt)
            response_format = response_struct_res
        elif version == "3":
            promptZero = {
                "role": "system", "content": f"""
You are an expert evaluator tasked with assessing a trainee's performance in a role-playing scenario. You will be provided with a transcript of the trainee's response. Your evaluation must be based *solely* on the criteria outlined in the **Evaluation Rubric** provided below.

**Your Task:**

Analyze the trainee's video response in the context of the **Interlocutor Profile** and the **Scenario Description**. Provide constructive feedback on how the trainee performed according to each criterion in the **Evaluation Rubric**. Identify specific strengths and areas for improvement.

**Contextual Information:**

**1. Interlocutor Profile:**
{interlocutor_profile}

**2. Scenario Description:**
{scenario_description}

**3. Evaluation Rubric:**
{evaluation_rubric}

**Instructions for Feedback:**

* For each criterion in the **Evaluation Rubric**, provide a specific assessment of the trainee's performance.
* Highlight what the trainee did well.
* Clearly point out areas where the trainee could improve.
* Provide actionable suggestions for improvement where applicable.
* The response must be in the second person (you should).
* Ensure your feedback is objective and directly related to the trainee's actions and words in the transcript, as judged against the provided rubric.
* Do not introduce any external criteria or assumptions beyond what is provided in the Interlocutor Profile, Scenario Description, and Evaluation Rubric.

 """
            }
            prompt=deepcopy(base_prompt_v3)
            prompt[0]= promptZero
        else:
            prompt = deepcopy(base_prompt_v3)
            response_format = response_struct_demo
            
        # Add response format instruction
        if(version!="3"):
            prompt[-1]['content'].append({
                "type": "text",
                "text": f"Please return response in json of format {response_format}. Now, Here are the video frames, audio spectrogram and transcription:\n"
            })
            prompt[-1]['content'].append({
            "type": "text",
            "text": f" Now, Here are the video frames, audio spectrogram and transcription:\n"
        })
        else:
            prompt[-1]['content'].append({
                "type": "text",
                "text": f"Now, Here is the transcription:\n"
            })

#             # response_format = response_struct_demo
            
        # Add response format instruction
        
        
        # Add spectrogram
        if(version!=3):
            prompt[-1]['content'].append({
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{spectrogram_image}"}
            })
        
        # Add transcription
        prompt[-1]['content'].append({
            "type": "text",
            "text": f"Transcription: \"{transcription_text}\"\n"
        })
        
        # Add video frames (use only a subset if there are many)
        max_frames_to_use = min(len(base64Frames), 100)  # Limit to 100 frames max
        frame_step = max(1, len(base64Frames) // max_frames_to_use)
        
        if(version!=3):
            for i in range(0, len(base64Frames), frame_step):
                if len(prompt[-1]['content']) >= 100:  # OpenAI has limits on content items
                    break
                    
                prompt[-1]['content'].append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64Frames[i]}",
                        "detail": "low"
                    }
                })
        
        # API call parameters
        if(version!="3"):
            params = {
                "model": "gpt-4o",
                "messages": prompt,
                "response_format": {"type": "json_object"},
                "max_tokens": 3000,
            }
        else: 
            params = {
                "model": "gpt-4o",
                "messages": prompt,
                "max_tokens": 3000,
            }
        
        # Make API call
        result = client.chat.completions.create(**params)
        output_text = result.choices[0].message.content
        
        # Clean up
        del spectrogram_image
        del prompt
        gc.collect()
        
        return output_text
    except Exception as e:
        logger.exception(f"Error processing video: {str(e)}")
        raise


def process_rubric_video(transcription_text, evaluation_rubric):
    """Process video with OpenAI API, optimized for memory and token usage."""
    try:
        



        promptZero = generate_dynamic_evaluation_prompt(evaluation_rubric['criteria'])        
        prompt=deepcopy(BASE_PROMPT_RUBRIC_EVAL)
        prompt[0]= promptZero
        

        prompt[-1]['content'].append({
            "type": "text",
            "text": f"Now, Here is the transcription:\n"
        })
        
        # Add transcription
        prompt[-1]['content'].append({
            "type": "text",
            "text": f"Transcription: \"{transcription_text}\"\n"
        })
        
        prompt[-1]['content'].append({
            "type": "text",
            "text": f"Here's an example json response structre {SAMPLE_RESPONSE_STRUCT}"
        })
    
        params = {
            "model": "gpt-4o",
            "messages": prompt,
            "max_tokens": 3000,
            "response_format": {"type": "json_object"},
        }
    
        # Make API call
        result = client.chat.completions.create(**params)
        output_text = result.choices[0].message.content
        
        
        gc.collect()
        
        return output_text
    except Exception as e:
        logger.exception(f"Error processing video: {str(e)}")
        raise


async def process_single_video_async(spectrogram_path, transcription_text, base64Frames, version):
    """Async version of process_single_video for concurrent processing."""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(
        None, 
        process_single_video, 
        spectrogram_path, 
        transcription_text, 
        base64Frames, 
        version
    )


def analyze_audio_from_video(video_path, version="1", interlocutor_profile=None, scenario_description=None, evaluation_rubric=None):
    """Main function to analyze audio from video with improved error handling and memory management."""
    start_time = time.time()
    audio_path = None
    spectrogram_path = None
    base64Frames = None
    
    try:
        
        if version == "3":
            # Extract audio
            audio_path = extract_audio_from_video(video_path)
            logger.info(f"Audio extracted successfully to: {audio_path}")
            
            # Verify file exists before continuing
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Extracted audio file not found at {audio_path}")
            
            # Check if audio is empty
            if is_audio_empty(audio_path):
                logger.warning("The extracted audio is empty or below threshold")
                raise ValueError("The extracted audio is empty or contains no meaningful sound.")
            
            logger.info("Audio contains sound, continuing with analysis")
            # Continue with other processing
            transcription_text = transcribe_audio(audio_path)
            output_text = process_rubric_video(transcription_text, json.loads(evaluation_rubric))
            
            return json.loads(output_text)

        logger.info(f"Starting analysis of video: {video_path} (version: {version})")
        
        # Extract audio
        audio_path = extract_audio_from_video(video_path)
        logger.info(f"Audio extracted successfully to: {audio_path}")
        
        # Verify file exists before continuing
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Extracted audio file not found at {audio_path}")
        
        # Check if audio is empty
        if is_audio_empty(audio_path):
            logger.warning("The extracted audio is empty or below threshold")
            raise ValueError("The extracted audio is empty or contains no meaningful sound.")
        
        logger.info("Audio contains sound, continuing with analysis")
        
        # Generate spectrogram first
        spectrogram_path = save_spectrogram(audio_path)
        logger.info(f"Spectrogram generated at: {spectrogram_path}")
        
        # Verify spectrogram exists
        if not os.path.exists(spectrogram_path):
            raise FileNotFoundError(f"Spectrogram file not found at {spectrogram_path}")
        
        # Continue with other processing
        transcription_text = transcribe_audio(audio_path)
        base64Frames = read_video(video_path)
        
        # Process video
        # Process video
        output_text = process_single_video(
            spectrogram_path, transcription_text, base64Frames, version, interlocutor_profile, scenario_description, evaluation_rubric)
        if version == '3':
            output = output_text
            return output
        output = json.loads(output_text)
        
        # Post-process results for version 2
        if version == '2':
            original_score = output.get('overallScore', 'Not provided')
            
            # Count positive responses
            facial_yes_count = sum(1 for value in output['facialExpressions'].values() if value == 'Yes')
            sound_yes_count = sum(1 for value in output['soundToneOfVoice'].values() if value == 'Yes')
            text_yes_count = sum(1 for value in output['textChoiceOfWords'].values() if value == 'Yes')
            
            # Calculate score
            correct_score = (1/3) * (facial_yes_count + sound_yes_count + text_yes_count)
            correct_score = np.ceil(correct_score)
            output['overallScore'] = round(correct_score, 1)
            
            logger.info(f"Score adjustment: {original_score} → {output['overallScore']}")
        
        end_time = time.time()
        logger.info(f"Processing completed in {end_time - start_time:.2f} seconds")
        
        return output
        
    except Exception as e:
        logger.exception(f"Error in analyze_audio_from_video: {str(e)}")
        raise
        
    finally:
        # Clean up resources
        try:
            if audio_path and os.path.exists(audio_path):
                logger.info(f"Cleaning up audio file: {audio_path}")
                os.remove(audio_path)
                
            if spectrogram_path and os.path.exists(spectrogram_path):
                logger.info(f"Cleaning up spectrogram file: {spectrogram_path}")
                os.remove(spectrogram_path)
                
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")
            
        # Force garbage collection
        del base64Frames
        gc.collect()


async def analyze_audio_from_video_async(video_path, version="1"):
    """Async version of the main analysis function."""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, analyze_audio_from_video, video_path, version)
