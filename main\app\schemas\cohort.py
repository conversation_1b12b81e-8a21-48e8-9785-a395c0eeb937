"""
This file contains all the Schemas 
of Cohort feature
"""

from pydantic import BaseModel
from datetime import datetime


class CohortBase(BaseModel):
    name: str | None = None


class CohortCreate(CohortBase):
    file_hash: str


class CohortUpdate(CohortBase):
    name: str | None


class CohortDelete(BaseModel):
    ...


class CohortInDBBase(CohortBase):
    id: int | None = None
    created_on: datetime
    last_updated: datetime

    class Config:
        orm_mode = True


class Cohort(CohortInDBBase):
    ...
    
    
class CohortReturn(CohortBase):
    id: int | None = None
    
    class Config:
        orm_mode = True

