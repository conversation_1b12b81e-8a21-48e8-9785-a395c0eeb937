from pydantic import BaseModel


class ActivityResponsesClass(BaseModel):

    CREATE_SUCCESS: dict = {
        "success": True,
        "message": "Activity created successfully.",
    }

    CREATE_ACTIVITY_EXISTS: dict = {
        "success": False,
        "message": "Activity already exists.",
    }

    CREATE_FAILED: dict = {
        "success": False,
        "message": "Failed to create activity.",
    }

    UPDATE_FAILED: dict = {
        "success": False,
        "message": "Failed to update.",
    }

    ACTIVITY_NOT_EXIST: dict = {
        "success": True,
        "message": "No activity found.",
    }

    ACTIVITY_DELETED_SUCCESS: dict = {
        "success": True,
        "message": "Activity deleted successfully.",
    }
    
    ACTIVITY_UPDATED: dict = {
        "success": True,
        "message": "Activity updated successfully.",
    }

    DELETE_FAILED: dict = {
        "success": False,
        "message": "Failed to delete activity.",
    }

ActivityResponses = ActivityResponsesClass()
