"""
This file contains the model mapping of 
role permission table which will have the
different roles against various permissions
"""

from datetime import datetime

from app.db.base_class import Base

from sqlalchemy import Column, DateTime, ForeignKey, Integer, UniqueConstraint


class RolePermission(Base):
    role_id = Column(Integer, ForeignKey("role.id"))
    permission_id = Column(
        Integer,
        ForeignKey("permission.id"),
    )
    created_on = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Role ID and Permission ID are unique together
    __table_args__ = (
        UniqueConstraint(
            "role_id", "permission_id", name="role_permission_uc"
        ),
    )
