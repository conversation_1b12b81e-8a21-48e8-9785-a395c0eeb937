"""
This file contains the model mapping of 
Tutorial
"""

from datetime import datetime, timezone

from app.db.base_class import Base

from sqlalchemy import (
    Column,
    DateTime,
    String,
)

class Tutorial(Base):
    name = Column(
        String(40), comment="question text", unique=True, nullable=False, index=True
    )
    video_file_name = Column(String, index=True)
    video_path = Column(String)
    practice_type = Column(String(40), nullable=False, default="mcqs")
    model_type = Column(String(100))
    created_on = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)