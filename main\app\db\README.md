# Core Database Management

Resources needed to manage database connection using SQLAlchemy.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)

## Description

This directory contain database management resources. The application uses SQLAlchemy to interact with the PostgreSQL database, and files in this directory performs the necessary configurations to make that to happen. There is no need to change these files unless there are significant changes how the database should function in the application.

For a more comprehensive look, check out the [official SQLAlchemy documentation](https://docs.sqlalchemy.org/en/20/tutorial/index.html).

## Usage

The files in this directory does the following:

- Initialize the PostgreSQL database.
- Connect to the database and create session.
- Create SQLAlchemy base class to be used by the entity models.
- Define a healthcheck method.
