"""
This file contains the model mapping of 
Pre Course Questions table
"""

from datetime import datetime, timezone

from app.db.base_class import Base

from sqlalchemy import (
    Column,
    DateTime,
    String,
    Integer,
    ForeignKey
)

class PreCourse(Base):
    course_id = Column(Integer, ForeignKey('course.id'))
    question = Column(
        String(400), comment="question text", nullable=False
    )
    created_on = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)