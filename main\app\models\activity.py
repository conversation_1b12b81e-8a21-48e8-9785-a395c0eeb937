"""
This file contains the model mapping of Activity
"""

from datetime import datetime, timezone

from .association_tables import CourseActivityAssociation
from app.db.base_class import Base
# from app.models.association_tables import course_activity_association
from sqlalchemy.orm import relationship

from sqlalchemy import (
    Column,
    DateTime,
    String,
    Enum,
    JSON, 
    Integer,
    ForeignKey
)
from enum import Enum as PyEnum

class ActivityType(PyEnum):
    pre_course_survey = 'pre_course_survey'
    quiz = 'quiz'
    tutorial = 'tutorial'
    hardware_test = 'hardware_test'
    video_example = 'video_example'
    plunge_activity = 'plunge_activity'
    practice_activity = 'practice_activity'
    assessment = 'assessment'
    boss_challenge = 'boss_challenge'
    recap = 'recap'
    feedback = 'feedback'
    additional_resources = 'additional_resources'
    

class Activity(Base):
    name = Column(String, comment="activity name")
    type = Column(Enum(ActivityType))
    questions = Column(JSON)
    video = Column(String, comment="path of video file if there is a video attached with activity")
    retries_allowed = Column(Integer, default=None)
    courses = relationship('Course', secondary=CourseActivityAssociation.__tablename__, back_populates='activities')
    mcqs = relationship('MCQS', back_populates='activity', cascade='all, delete-orphan')
    extra_fields = Column(JSON)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                        onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    
    
class MCQS(Base):
    activity_id = Column(Integer, ForeignKey('activity.id'))
    question = Column(String, comment="question text")
    options = Column(JSON)
    right_option = Column(String)
    activity = relationship('Activity', back_populates='mcqs')
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                        onupdate=lambda: datetime.now(timezone.utc), nullable=False)