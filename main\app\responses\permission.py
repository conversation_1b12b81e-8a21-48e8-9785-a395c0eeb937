"""
This file contains all the responses 
of Permissions related API's
"""

from pydantic import BaseModel


class PermissionResponsesClass(BaseModel):
    PERMISSION_CREATE_SUCCESS: dict = {
        "success": True,
        "message": "Permission created successfully.",
    }

    PERMISSION_UPDATE_SUCCESS: dict = {
        "success": True,
        "message": "Permission updated successfully.",
    }

    PERMISSION_CREATE_NOT_ALLOWED: dict = {
        "success": False,
        "message": "You are not allowed to create Permission.",
    }

    PERMISSION_DELETE_SUCCESS: dict = {
        "success": True,
        "message": "Permission deleted successfully.",
    }

    GET_SUCCESS: dict = {
        "success": True,
        "message": "Permission fetched successfully.",
    }

    ALL_PERMISSION: dict = {
        "success": True,
        "message": "All Permissions.",
    }


PermissionResponses = PermissionResponsesClass()
