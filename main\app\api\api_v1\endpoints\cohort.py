"""
This file contains all the API's related to course
"""

import random

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.scopes import CohortScopes
from app.responses import GeneralResponses
from app.logger import logger

from fastapi import APIRouter, Depends, Security, status, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from fastapi_pagination.ext.sqlalchemy import paginate
from typing import List

router = APIRouter()


@router.get(
    "/get/all",
    status_code=status.HTTP_200_OK,
    response_model=List[schemas.CohortReturn]
)
def get_all_courses(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[CohortScopes.read]
    ),
) -> JSONResponse:
    """Get all courses

    Args:\n
        request_data


    """
    try:
        return crud.cohort.get_all_cohorts(db=db)
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

