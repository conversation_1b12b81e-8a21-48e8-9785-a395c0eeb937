
FROM python:3.10-slim AS builder

# Create a non-root user
RUN addgroup --system app && adduser --system --group app

# Install uv
RUN pip install uv

# Copy requirements files
ENV UV_COMPILE_BYTECODE=1 UV_LINK_MODE=copy

# Disable Python downloads, because we want to use the system interpreter
# across both images. If using a managed Python version, it needs to be
# copied from the build image into the final image; see `standalone.Dockerfile`
# for an example.
ENV UV_PYTHON_DOWNLOADS=0

WORKDIR /app
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=./main/uv.lock,target=uv.lock \
    --mount=type=bind,source=./main/pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project --no-dev
ADD ./main/ /app/
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen 

FROM python:3.10-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on

# Set apt configurations for better reliability
RUN echo 'Acquire::http::Timeout "120";' >> /etc/apt/apt.conf.d/99timeout \
    && echo 'Acquire::Retries "3";' >> /etc/apt/apt.conf.d/99timeout

# Update and install dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        wget \
        graphviz \
        curl \
        libpq5 \
        libpq-dev \
        build-essential \
        libblas-dev \
        liblapack-dev \
        gfortran \
        libgl1 \
        ffmpeg \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder --chown=app:app /app /app

WORKDIR /app
# Place executables in the environment at the front of the path
ENV PATH="/app/.venv/bin:$PATH"



CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:5000", "app.main:app"]