"""
This file contains all the API's related to activity
"""

from app import crud, schemas, models
from app.api import deps
from app.responses.base import APIResponse, APIException
from app.core.activity_progress import new_analyze_audio_from_video, analyze_audio_from_video, analyze_listening_part, analyze_listening_part_demo
from app.scopes import ActivityProgressScopes, UserScopes, AuthScopes
from app.responses import ActivityProgressResponses, GeneralResponses, AuthResponses, RoleResponses
from app.logger import logger
from app.utils.globals import create_info_logging_message

from fastapi import APIRouter, Depends, Security, status, BackgroundTasks, File, UploadFile, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from typing import List

import tempfile
import os
import json
import gc
import asyncio

async def run_both_analysis(temp_path, video_type):
    # Create a copy of the temp file for each analysis to prevent file access conflicts
    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as temp_copy1, \
         tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as temp_copy2:
        
        # Read the original file and write to both copies
        with open(temp_path, 'rb') as original:
            file_content = original.read()
            temp_copy1.write(file_content)
            temp_copy2.write(file_content)
        
        # Use separate file paths for each analysis
        temp_path1 = temp_copy1.name
        temp_path2 = temp_copy2.name
    
    try:
        # Run analyses with separate file paths
        task1 = asyncio.create_task(asyncio.to_thread(
                        new_analyze_audio_from_video, video_path=temp_path1, video_type=video_type))
        task2 = asyncio.create_task(asyncio.to_thread(
                        analyze_audio_from_video, video_path=temp_path2, version="2"))
        
        # Wait for both to complete
        new_result, standard_result = await asyncio.gather(task1, task2)
        
        # Combine results
        combined_result = {
            "new_analysis": new_result,
            "standard_analysis": standard_result
        }
        return combined_result
    finally:
        # Clean up the temporary files
        for path in [temp_path1, temp_path2]:
            try:
                if os.path.exists(path):
                    os.remove(path)
            except Exception as e:
                logger.error(f"Error removing temporary file {path}: {e}")

# import tracemalloc

router = APIRouter()

def calculate_average_of_ai_activity(data):
    result = {
        "responding": {},
        "listening": {},
    }
    
    for key in ["facial_expressions", "eye_contact", "body_movement_and_posture", "gestures", "tone_and_manner_of_speech", "choice_of_words"]:
        for res_key in result:
            for each in data:
                res = each.result
                if res and res.get(res_key, {}).get(res_key):
                    val = res[res_key][res_key].get(key, 0)
                    if key in result[res_key]:
                        result[res_key][key].append(val)
                    else:
                        result[res_key][key] = [val]
            if res_key in result and key in result[res_key]:
                result[res_key][key] = int(round(sum(result[res_key][key])/len(result[res_key][key])))
                    
    # To make structure consistent with the result stored in db against each user
    result['responding'] = {'responding': result['responding']}
    result['listening'] = {'listening': result['listening']}
    
    return result

@router.post(
    "/create",
    status_code=status.HTTP_200_OK,
    response_model=schemas.ActivityProgressReturn,
)
async def create_activity_progress(
    *,
    # background_tasks: BackgroundTasks,
    course_id: int = Form(...),
    activity_id: int = Form(...),
    user_input: str = Form(default=""),
    video_tag: str = Form(default=""),
    video_file: UploadFile = File(default=None),
    db: Session = Depends(deps.get_db),
    # email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ActivityProgressScopes.create]
    ),
) -> JSONResponse:
    """Register new activity progress

    Args:\n
        request_data


    """
    # tracemalloc.start(5)
    course_in = crud.course.get(db=db, id=course_id)
    if not course_in:
        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="activity_progress",
                message=str(ActivityProgressResponses.INVALID_COURSE_ID["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **ActivityProgressResponses.INVALID_COURSE_ID,
                data={},
            ),
        )
    current_user_courses = [e.course.id for e in current_user.courses_progress]
    if course_id not in current_user_courses:
        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="activity_progress",
                message=str(ActivityProgressResponses.NOT_ENROLLED["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **ActivityProgressResponses.NOT_ENROLLED,
                data={},
            ),
        )
    activity = list(filter(lambda e: e.id == activity_id, course_in.activities))
    if not activity:
        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="activity_progress",
                message=str(ActivityProgressResponses.INVALID_ACTIVITY_ID["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **ActivityProgressResponses.INVALID_ACTIVITY_ID,
                data={},
            ),
        )
    activity = activity[0]
    if user_input:
        try:
            user_input_json = json.loads(user_input)
        except Exception:
            logger.info(
                create_info_logging_message(
                    endpoint="/create",
                    feature="activity_progress",
                    message=str(ActivityProgressResponses.INVALID_JSON["message"]),
                )
            )
            return JSONResponse(
                status_code=200,
                content=APIResponse(
                    **ActivityProgressResponses.INVALID_JSON,
                    data={},
                ),
            )
    else:
        user_input_json = [{}]
    activity_types = models.ActivityType
    activity_type = activity.type
    try:
        if activity_type in [activity_types.practice_activity, activity_types.boss_challenge]:
            with tempfile.NamedTemporaryFile(delete=False) as temp:
                temp.write(await video_file.read())
                temp_path = temp.name

            activity = crud.activity.get(db=db, id=activity_id)
            interLocutorProfile = None
            scenarioDescription = None
            evaluationRubric = None
            if activity.extra_fields and activity.extra_fields.get("practiceActivityData"):
                practiceActivityData = activity.extra_fields["practiceActivityData"]
                interLocutorProfile = practiceActivityData.get("interLocutorProfile") if practiceActivityData.get("interLocutorProfile") else None
                scenarioDescription = practiceActivityData.get("scenarioDescription") if practiceActivityData.get("scenarioDescription") else None
                evaluationRubric = json.dumps(practiceActivityData.get("evaluationRubric")) if practiceActivityData.get("evaluationRubric") else None
      
                
            # result = process_video_and_analyze(video_path=temp_path)
            if video_tag == "listening":
                result = analyze_listening_part(video_path=temp_path)
            else:
                result = analyze_audio_from_video(video_path=temp_path, version="3", interlocutor_profile=interLocutorProfile, scenario_description=scenarioDescription, evaluation_rubric=evaluationRubric)
            # result = analyze_audio_from_video(video_path=temp_path)
        elif activity_type == activity_types.quiz:
            correct = incorrect = 0
            mcq_result = []
            for each in user_input_json:
                mcq = list(filter(lambda e: e.id == each["id"], activity.mcqs))[0]
                is_selected_correct = each["selectedOption"].lower() == mcq.right_option.lower()
                if activity.extra_fields and activity.extra_fields.get("show_correct_option"):
                    mcq_result.append({"id": mcq.id, "result": is_selected_correct, "right_option": mcq.right_option})
                else:
                    mcq_result.append({"id": mcq.id, "result": is_selected_correct, "right_option": ""})
                if is_selected_correct:
                    correct += 1
                else:
                    incorrect += 1
            result = {
                "mcq_result": mcq_result,
                "correct": correct,
                "incorrect": incorrect,
                "percentage": (correct/(correct + incorrect)) * 100
            }
        else:
            result = {}
        
        activity_progress = crud.activity_progress.get_by_user_course_and_activity(db=db, user_id=current_user.id, 
                                                                                   course_id=course_id, activity_id=activity_id)
        if activity_type in [activity_types.practice_activity, activity_types.boss_challenge]:
                db_result = activity_progress.result if activity_progress and activity_progress.result else {}
                if video_tag == "listening":
                    db_result.update({"listening": result})
                else:
                    db_result.update({"responding": result})
        else:
            db_result = result
        if activity_progress:
            # activity_progress_update_in = schemas.ActivityProgressUpdate(
            #     course_id=course_id,
            #     activity_id=activity_id,
            #     user_id=current_user.id,
            #     user_input=user_input_json, 
            #     result=db_result
            #     )
            db.query(models.ActivityProgress).filter(models.ActivityProgress.id == activity_progress.id).update({"result": db_result})
            db.commit()
            # activity_progress_out = crud.activity_progress.update(db=db, db_obj=activity_progress, obj_in=activity_progress_update_in)
        else:
            # Register activity progress in Activity Progress table
            activity_progress_in = schemas.ActivityProgressInDB(
                course_id=course_id,
                activity_id=activity_id,
                user_id=current_user.id,
                user_input=user_input_json,
                result=db_result,
            )
            activity_progress_out = crud.activity_progress.create(db=db, obj_in=activity_progress_in)
            # Caluculate course progress percentage and update course progress
            course_progress = crud.course_progress.get_by_user_and_course(db=db, user_id=current_user.id, course_id=course_id)
            total_activities_count = len(crud.course.get(db=db, id=course_id).activities)
            progress_percentage = course_progress.progress_percentage + (100/total_activities_count)
            if progress_percentage > 100:
                progress_percentage = 100
            course_progress_in = schemas.CourseProgressUpdate(
                progress_percentage=progress_percentage
            )
            crud.course_progress.update(db=db, db_obj=course_progress, obj_in=course_progress_in)
        
        next_activity_link = ''
        course_activity_associations = db.query(models.CourseActivityAssociation).filter(models.CourseActivityAssociation.course_id == 
                                                                                         course_id).all()
        for idx, csa in enumerate(course_activity_associations):
            if csa.activity_id == activity_id:
                if idx < len(course_activity_associations) - 1:
                    next_activity = crud.activity.get(db=db, id=course_activity_associations[idx + 1].activity_id)
                    next_activity_link = f"/{models.ActivityType(next_activity.type).value}?course_id={course_id}&activity_id={next_activity.id}"
                    break
                else:
                    next_activity_link = "/completed-courses"
        logger.info(
            create_info_logging_message(
                endpoint="/create",
                feature="activity_progress",
                message=str(ActivityProgressResponses.CREATE_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **ActivityProgressResponses.CREATE_SUCCESS,
                data={
                    "result": result,
                    "next_activity_link": next_activity_link,
                },
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**ActivityProgressResponses.CREATE_FAILED),
        )
    finally:
        if video_file:
            os.remove(temp_path)
        collected = gc.collect()
        print(f"Garbage collector: collected {collected} objects.")
        # snapshot1 = tracemalloc.take_snapshot()
        # for stat in snapshot1.statistics("lineno"):
        #     print(stat)
        # tracemalloc.clear_traces()
        # tracemalloc.stop()
        
        
@router.post(
    "/ai-activity",
    status_code=status.HTTP_200_OK,
)
async def run_ai_activity(
    # background_tasks: BackgroundTasks,
    video_file: UploadFile = File(default=None),
    video_tag: str = Form(default=""),
    video_type: str = Form(default="food_complaint"),
    demo: bool = Form(default=False),
) -> JSONResponse:
    """Register new activity progress

    Args:\n
        request_data
    """
    try:
        with tempfile.NamedTemporaryFile(delete=False) as temp:
            temp.write(await video_file.read())
            temp_path = temp.name
        if video_tag == "listening":
            return analyze_listening_part_demo(video_path=temp_path)
        else:
            if demo:
                return await run_both_analysis(temp_path, video_type)
                # return new_analyze_audio_from_video(video_path=temp_path, video_type=video_type)
            return analyze_audio_from_video(video_path=temp_path, version="2")
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=APIResponse(**ActivityProgressResponses.CREATE_FAILED),
        )
    finally:
        if video_file:
            os.remove(temp_path)
        collected = gc.collect()
        print(f"Garbage collector: collected {collected} objects.")
                     


@router.patch(
    "/update/{activity_progress_id}",
    status_code=status.HTTP_200_OK,
    response_model=schemas.ActivityProgressReturn,
)
def activity_progress_update(
    *,
    activity_progress_id: int,
    activity_progress_update_in: schemas.ActivityProgressUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    # email_client: EmailClient = Depends(deps.get_email_client),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ActivityProgressScopes.update]
    ),
) -> JSONResponse:
    """Update user data

    Args:\n
        request_data


    """
    try:
        activity_progress = crud.activity_progress.get(db=db, id=activity_progress_id)
        # Verify if user exists in system
        if not activity_progress:
            logger.info(
                create_info_logging_message(
                    endpoint="/update",
                    feature="activity_progress",
                    message=str(ActivityProgressResponses.NOT_EXIST["message"]),
                )
            )
            return JSONResponse(
                    status_code=404,
                    content=APIResponse(**ActivityProgressResponses.NOT_EXIST),
                )

        # Update User in the DB
        background_tasks.add_task(
            crud.activity_progress.update(db=db, db_obj=activity_progress, obj_in=activity_progress_update_in)
        )
        
        logger.info(
            create_info_logging_message(
                endpoint="/update",
                feature="activity_progress",
                message=str(ActivityProgressResponses.UPDATED_SUCCESS["message"]),
            )
        )
        return JSONResponse(
            status_code=200,
            content=APIResponse(
                **ActivityProgressResponses.UPDATED_SUCCESS,
            ),
        )
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


# @router.delete(
#     "/delete/{activity_id}",
#     status_code=status.HTTP_200_OK,
# )
# def activity_progress_delete(
#     *,
#     activity_id: int,
#     background_tasks: BackgroundTasks,
#     db: Session = Depends(deps.get_db),
#     current_user: models.User = Security(
#         deps.get_current_user, scopes=[ActivityProgressScopes.delete]
#     ),
# ) -> JSONResponse:
#     """Delete activity data

#     Args:\n
#         request_data


#     """
#     try:
#         activity = crud.activity_progress.get(db=db, id=activity_id)
#         # Verify if user exists in system
#         if not activity:
#             logger.info(
#                 create_info_logging_message(
#                     endpoint="/delete",
#                     feature="activity_progress",
#                     message=str(ActivityProgressResponses.NOT_EXIST["message"]),
#                 )
#             )
#             return JSONResponse(
#                 status_code=404,
#                 content=APIResponse(**ActivityProgressResponses.NOT_EXIST),
#             )
        
#         # Update User in the DB
#         background_tasks.add_task(crud.activity_progress.remove(db=db, id=activity_id))
        
#         logger.info(
#             create_info_logging_message(
#                 endpoint="/delete",
#                 feature="activity_progress",
#                 message=str(ActivityProgressResponses.DELETED_SUCCESS["message"]),
#             )
#         )
#         return JSONResponse(
#             status_code=200,
#             content=APIResponse(
#                 **ActivityProgressResponses.DELETED_SUCCESS,
#             ),
#         )
#     except Exception as e:
#         logger.exception(e)
#         raise APIException(
#             status_code=status.HTTP_400_BAD_REQUEST,
#             message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
#         )

@router.get(
    "/get/all/{course_id}",
    status_code=status.HTTP_200_OK,
    response_model=Page[schemas.ActivityProgressReturn]
)
def get_all_activities_of_course(
    course_id: int,
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[ActivityProgressScopes.read]
    ),
) -> JSONResponse:
    """Get all activities progress in a course of a user

    Args:\n
        request_data


    """
    try:
        return crud.activity_progress.get_by_user_course_id(db=db, user_id=current_user.id, course_id=course_id)
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/get/result",
    status_code=status.HTTP_200_OK,
    response_model=List[schemas.ActivityProgressResult]
)
def get_all_activity_of_user_by_activity_type(
    user_id: int,
    activity_type: str,
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.read]
    ),
) -> JSONResponse:
    """Get all activities progress in a course of a user

    Args:\n
        request_data


    """
    try:
        return crud.activity_progress.get_by_user_activity_type(db=db, user_id=user_id, activity_type=activity_type)
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )


@router.get(
    "/get/result-by-cohort",
    status_code=status.HTTP_200_OK,
    response_model=schemas.ActivityProgressCohortResult
)
def get_all_activity_of_cohort_by_activity_type(
    cohort_id: int,
    activity_type: str,
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.read]
    ),
) -> JSONResponse:
    """Get all activities progress in a course of a user

    Args:\n
        request_data


    """
    try:
        results = crud.activity_progress.get_by_cohort_activity_type(db=db, cohort_id=cohort_id, activity_type=activity_type)
        if activity_type == "boss_challenge":
            return {"result": calculate_average_of_ai_activity(results)}
        return {"result": {}}
    except Exception as e:
        logger.exception(e)
        raise APIException(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=APIResponse(**GeneralResponses.GENERAL_EXCEPTION),
        )

