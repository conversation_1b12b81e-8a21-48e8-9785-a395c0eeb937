"""
This file contains all the crud operations for activity progress
"""

from typing import Optional

from app.crud.base import CRUDBase
from app.models.activity_progress import ActivityProgress
from app.models.activity import Activity
from app.models.user import User
from app.schemas.activity_progress import ActivityProgressCreate, ActivityProgressUpdate

from sqlalchemy.orm import Session


class CRUDActivityProgress(CRUDBase[ActivityProgressUpdate, ActivityProgressCreate, ActivityProgressUpdate]):

    def get_by_id(self, db: Session, *, id: int) -> Optional[ActivityProgress]:
        return db.query(ActivityProgress).filter(ActivityProgress.id == id).first()

    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[ActivityProgress]:
        return db.query(ActivityProgress).filter(ActivityProgress.user_id == user_id).all()

    def get_by_course_id(self, db: Session, *, course_id: int) -> Optional[ActivityProgress]:
        return db.query(ActivityProgress).filter(ActivityProgress.course_id == course_id).all()

    def get_by_user_course_id(self, db: Session, *, user_id: int, course_id: int) -> Optional[ActivityProgress]:
        return db.query(ActivityProgress).filter(ActivityProgress.user_id == user_id).filter(ActivityProgress.course_id == course_id).all()

    def get_by_user_course_and_activity(self, db: Session, *, user_id: int, course_id: int, activity_id: int) -> Optional[ActivityProgress]:
        return db.query(ActivityProgress).filter(ActivityProgress.user_id == user_id).\
            filter(ActivityProgress.course_id == course_id).filter(ActivityProgress.activity_id == activity_id).first()

    def get_by_user_activity_type(self, db: Session, *, user_id: int, activity_type: str) -> Optional[list[ActivityProgress]]:
        activity_ids = db.query(Activity.id).filter(Activity.type == activity_type).distinct()
        return db.query(ActivityProgress).filter(ActivityProgress.user_id == user_id).\
            filter(ActivityProgress.activity_id.in_(activity_ids)).all()

    def get_by_cohort_activity_type(self, db: Session, *, cohort_id: int, activity_type: str) -> Optional[list[ActivityProgress]]:
        activity_ids = db.query(Activity.id).filter(Activity.type == activity_type).distinct()
        user_ids = db.query(User.id).filter(User.cohort_id == cohort_id).distinct()
        return db.query(ActivityProgress).filter(ActivityProgress.user_id.in_(user_ids)).\
            filter(ActivityProgress.activity_id.in_(activity_ids)).all()
            
    def get_completed_activities_of_user(self, db: Session, user_id: int, course_id: int) -> Optional[list[int]]:
        return db.query(ActivityProgress.activity_id).filter(ActivityProgress.user_id == user_id).\
            filter(ActivityProgress.course_id == course_id).all()

    def get_all_activity_progress(self, db: Session) -> list[ActivityProgress]:
        return db.query(ActivityProgress).all()


activity_progress = CRUDActivityProgress(ActivityProgress)
