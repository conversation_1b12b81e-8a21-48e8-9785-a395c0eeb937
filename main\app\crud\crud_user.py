"""
This file contains all the crud operations for 
role related things
"""

from typing import Optional, List
from datetime import datetime

from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserInvite, UserUpdate

from sqlalchemy.orm import Session


class CRUDUser(CRUDBase[User, UserInvite, UserUpdate]):
    def get_by_name(self, db: Session, name: str) -> Optional[User]:
        return db.query(User).filter(User.name == name).first()
    
    def get_by_email(self, db: Session, email: str) -> Optional[User]:
        return db.query(User).filter(User.email == email).first()
    
    def get_by_cohort_id(self, db: Session, cohort_id: str) -> Optional[List[User]]:
        return db.query(User).filter(User.cohort_id == cohort_id).all()
    
    def get_cohort_user_id_list(self, db: Session, cohort_id: int) -> Optional[List[User]]:
        return db.query(User.id).filter(User.cohort_id == cohort_id).all()
    
    def update_last_login(self, db: Session, db_obj: User):
        db_obj.last_login = datetime.utcnow()
        db.add(db_obj)
        db.commit()
        return db_obj
    
    def update_password_hash(self, db: Session, db_obj: User, hashed_password: str):
        db_obj.hashed_password = hashed_password
        db.add(db_obj)
        db.commit()
        return db_obj

user = CRUDUser(User)
