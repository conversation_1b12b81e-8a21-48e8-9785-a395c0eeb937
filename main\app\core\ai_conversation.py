from openai import OpenAI
import os
from dotenv import load_dotenv


# Load the .env file
load_dotenv()

# Get the API key
api_key = os.getenv('OUR_OPENAI_API_KEY')


def get_chat_response(prompt, user_type):
    
    client = OpenAI(api_key=api_key)

    INSTRUCTOR_PROMPT = (
        "You are an AI Instructor trained to help instructors with questions about the dignity course. "
        "You are an AI Instructor coach and part of Skillseed's team if someone asks about yourself. "
        "Do not answer any question that is not related to dignity. "
        "Please give a brief answer."
        )


    ADMIN_PROMPT = (
        "You are an AI coach trained to help Ad<PERSON> of dognity course  with administrative questions about the dignity course. "
        "You are an AI coach and part of <PERSON>eed's team if someone asks about yourself. "
        "Do not answer any question that is not related to dignity. "
        "Please give a brief answer."
    )

    LEARNER_PROMPT = (
        "You are an AI coach trained to help user with questions about maintaining dignity.You  are a part of Skillseed's team and here to help in case users have any questions about the dignity course only. "
        "You are an AI coach and part of <PERSON><PERSON>'s team if someone asks about yourself."
        "Do not answer any question that is not related to dignity."
        "Please give a brief answer."
    )

    if user_type == "admin":
        SYSTEM_PROMPT = ADMIN_PROMPT
    elif user_type == "instructor":
        SYSTEM_PROMPT = INSTRUCTOR_PROMPT
    else:
        SYSTEM_PROMPT = LEARNER_PROMPT

    stream = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": prompt}
        ],
        stream=True  # Enable streaming for incremental response handling
    )

    output = ""
    for chunk in stream:
        output += chunk.choices[0].delta.content or ""

    return output


def get_response_from_chatbot(message: str, user_type: str):
    return get_chat_response(message, user_type)
