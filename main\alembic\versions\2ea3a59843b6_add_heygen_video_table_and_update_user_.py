"""Add heygen video table and update user table

Revision ID: 2ea3a59843b6
Revises: a1f2625dcfec
Create Date: 2025-05-12 00:15:42.832480

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2ea3a59843b6'
down_revision = 'a1f2625dcfec'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('heygen_video',
    sa.<PERSON>umn('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False, comment='Name of the Heygen video'),
    sa.Column('s3_path', sa.String(), nullable=False, comment='Path of the video file in S3 bucket'),
    sa.Column('instructor_id', sa.Integer(), nullable=False, comment='ID of the instructor who created this video'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['instructor_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_heygen_video_id'), 'heygen_video', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_heygen_video_id'), table_name='heygen_video')
    op.drop_table('heygen_video')
    # ### end Alembic commands ###
