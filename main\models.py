# coding: utf-8
from sqlalchemy import Column, DateTime, Float, ForeignKey, Integer, JSON, String, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class Activity(Base):
    __tablename__ = 'activity'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    type = Column(String(17))
    questions = Column(JSON)
    video = Column(String)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)


class Cohort(Base):
    __tablename__ = 'cohort'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(40))
    file_hash = Column(String(100), unique=True)
    file_path = Column(String(200))
    created_on = Column(DateTime, nullable=False)
    last_updated = Column(DateTime, nullable=False)


class Permission(Base):
    __tablename__ = 'permission'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)
    created_on = Column(DateTime, nullable=False)
    last_updated = Column(DateTime, nullable=False)


class Role(Base):
    __tablename__ = 'role'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)
    created_on = Column(DateTime, nullable=False)
    last_updated = Column(DateTime, nullable=False)


class MCQ(Base):
    __tablename__ = 'm_c_q_s'

    id = Column(Integer, primary_key=True, index=True)
    activity_id = Column(ForeignKey('activity.id'))
    question = Column(String)
    options = Column(JSON)
    right_option = Column(String)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

    activity = relationship('Activity')


class RolePermission(Base):
    __tablename__ = 'role_permission'
    __table_args__ = (
        UniqueConstraint('role_id', 'permission_id'),
    )

    id = Column(Integer, primary_key=True, index=True)
    role_id = Column(ForeignKey('role.id'))
    permission_id = Column(ForeignKey('permission.id'))
    created_on = Column(DateTime, nullable=False)
    last_updated = Column(DateTime, nullable=False)

    permission = relationship('Permission')
    role = relationship('Role')


class User(Base):
    __tablename__ = 'user'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(40), nullable=False)
    IC = Column(String(40), nullable=False)
    email = Column(String(255), nullable=False)
    role_id = Column(ForeignKey('role.id', ondelete='RESTRICT'), nullable=False, index=True)
    cohort_id = Column(ForeignKey('cohort.id', ondelete='RESTRICT'), index=True)
    hashed_password = Column(String(256), nullable=False)
    last_login = Column(DateTime)
    created_on = Column(DateTime, nullable=False)
    last_updated = Column(DateTime, nullable=False)

    cohort = relationship('Cohort')
    role = relationship('Role')


class Course(Base):
    __tablename__ = 'course'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    instructor = Column(ForeignKey('user.id'))
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

    user = relationship('User')


class ActivityProgres(Base):
    __tablename__ = 'activity_progress'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(ForeignKey('user.id', ondelete='RESTRICT'), nullable=False, index=True)
    course_id = Column(ForeignKey('course.id', ondelete='RESTRICT'), nullable=False, index=True)
    activity_id = Column(ForeignKey('activity.id', ondelete='RESTRICT'), nullable=False, index=True)
    user_input = Column(JSON)
    result = Column(JSON)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

    activity = relationship('Activity')
    course = relationship('Course')
    user = relationship('User')


class CourseActivityAssociation(Base):
    __tablename__ = 'course_activity_association'

    id = Column(Integer, primary_key=True, index=True)
    course_id = Column(ForeignKey('course.id'))
    activity_id = Column(ForeignKey('activity.id'))
    sequence = Column(Integer)

    activity = relationship('Activity')
    course = relationship('Course')


class PreCourse(Base):
    __tablename__ = 'pre_course'

    id = Column(Integer, primary_key=True, index=True)
    course_id = Column(ForeignKey('course.id'))
    question = Column(String(400), nullable=False)
    created_on = Column(DateTime, nullable=False)

    course = relationship('Course')


class CourseProgres(Base):
    __tablename__ = 'course_progress'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(ForeignKey('user.id', ondelete='RESTRICT'), nullable=False, index=True)
    course_id = Column(ForeignKey('course.id', ondelete='RESTRICT'), nullable=False, index=True)
    last_activity_progress = Column(ForeignKey('activity_progress.id', ondelete='RESTRICT'))
    progress_percentage = Column(Float)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

    course = relationship('Course')
    activity_progres = relationship('ActivityProgres')
    user = relationship('User')
