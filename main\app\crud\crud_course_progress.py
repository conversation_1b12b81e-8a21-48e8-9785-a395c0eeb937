"""
This file contains all the crud operations for course
"""

from typing import Optional

from app.crud.base import CRUDBase
from app.models.course_progress import CourseProgress
from app.schemas.course_progress import CourseProgressCreate, CourseProgressUpdate

from sqlalchemy.orm import Session


class CRUDCourseProgress(CRUDBase[CourseProgress, CourseProgressCreate, CourseProgressUpdate]):

    def get_by_id(self, db: Session, *, id: int) -> Optional[CourseProgress]:
        return db.query(CourseProgress).filter(CourseProgress.id == id).first()
    
    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[CourseProgress]:
        return db.query(CourseProgress).filter(CourseProgress.user_id == user_id).all()
    
    def get_by_user_and_course(self, db: Session, *, course_id: int, user_id: int) -> Optional[CourseProgress]:
        return db.query(CourseProgress).filter(CourseProgress.user_id == user_id).filter(CourseProgress.course_id == course_id).first()

    def remove_by_course(self, db: Session, *, course_id: int) -> None:
        db.query(CourseProgress).filter(CourseProgress.course_id == course_id).delete()
        db.commit()


course_progress = CRUDCourseProgress(CourseProgress)
