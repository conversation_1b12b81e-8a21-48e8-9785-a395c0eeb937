"""
This file contains all the Scopes 
of Organization related API's/resources
"""

from pydantic import BaseModel


class OrganizationScopesClass(BaseModel):
    create: str = "organization:create"
    read: str = "organization:read"
    update: str = "organization:update"
    delete: str = "organization:delete"
    update_status: str = "organization:update_status"
    create_admin: str = "organization:create_admin"


OrganizationScopes = OrganizationScopesClass()
