"""
This file contains the model mapping of Course
"""

from datetime import datetime, timezone

from .association_tables import CourseActivityAssociation
from app.db.base_class import Base
# from app.models.association_tables import course_activity_association
from sqlalchemy.orm import relationship

from sqlalchemy import (
    Column,
    DateTime,
    String,
    Integer,
    ForeignKey
)

class Course(Base):
    name = Column(String(80), comment="course name")
    instructor = Column(Integer, ForeignKey('user.id'))
    description = Column(String(500), comment="course description")
    activities = relationship('Activity', secondary=CourseActivityAssociation.__tablename__, back_populates='courses', lazy="joined")
    # users = relationship("CourseProgress", back_populates="course")
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                        onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    

    
