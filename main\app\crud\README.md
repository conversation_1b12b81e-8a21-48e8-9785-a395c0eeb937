# Database Transaction Logic

A collection of SQLAlchemy ORM transactions.

## Table of Contents

1. [Description](#description)
2. [Usage](#usage)
3. [Example](#example)

## Description

Database transactions and queries for each entity exists inside a corresponding module. This is used to decouple the application from the low-level database transaction logic and to decouple transactions based on entities. SQLAlchemy is used to access the database, therefore data is manipulated using ORM methods.

Take a loot at the documentation on how to:

- [Query using SQLAlchemy ORM](https://docs.sqlalchemy.org/en/20/orm/queryguide/index.html).
- [Use the SQLAlchemy database session object](https://docs.sqlalchemy.org/en/20/orm/queryguide/index.html).

For a more comprehensive look, check out the [official SQLAlchemy documentation](https://docs.sqlalchemy.org/en/20/tutorial/index.html).

## Usage

Transaction methods are organized into classes that corresponds with an entity. Each entity is defined as SQLAlchemy model inside the `models` directory.

All crud classes must inherit the `CRUDBase` class (found in `base.py`). This class provides the generic `get`, `list`, `create`, `update`, and `remove` transaction methods. To inherit this class, we need to override the following generics:

| Generic            | Type                      | Purpose                                                                                                                                                                                                                     |
| ------------------ | ------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `ModelType`        | SQLAlchemy mapping object | Transfer object between the application and the database. A query will return an instance of this object and the update transaction will use an instance of this object to modify a row. Found in the `models` directory.   |
| `CreateSchemaType` | Pydantic schema model     | Validation class that defines what kind of object is allowed to be used to create a new row. In other words, what defining what information are needed to create a new data object. Found in the `schemas` directory.       |
| `UpdateSchemaType` | Pydantic schema model     | Validation class that defines what kind of object is allowed to be used to update a new row. In other words, what defining what information are needed to update an existing data object. Found in the `schemas` directory. |

_NOTE: **DO NOT** re-implement generic `get`, `get_multi`, `create`, `update`, and `remove` methods. Those are already implemented in `CRUDBase`. Only implement crud methods that are specific to your usecase._

To add a new transaction or modify an existing transaction you need to:

- Identify the entity you want to perform the transaction on.
- If the crud file doesn't already exist for it, then create a new one. Otherwise, use an existing one.
- Identify the entities that are needed to perform the transaction.
- Create a new crud class that inherits `CRUDBase`. Override the three generic types, using objects that is associated with the entity we are creating transaction logic for: `ModelType`, `CreateSchemaType`, and `UpdateSchemaType`.
- Create transaction methods using the SQLAlchemy session object.
- Import the specific crud file in `__init__.py`.

To use the crud methods, simply import the crud object and invoke the method:

- Import the crud object.
- Get the SQLAlchemy session object from the `deps.py` dependency file.
- Invoke the transaction method, providing it with the session object.

The table below shows the relationship between crud files and model files at the time of writing. It shows a list of crud files, the entities needed for transactions in each file, and the model files that defines each entity:

| CRUD File                                | Model Files                                                                                                       | Entities                                                                                      |
| ---------------------------------------- | ----------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- |
| `crud_permission.py`                     | `permission.py`                                                                                                   | `Permission`                                                                                  |
| `crud_role_permission.py`                | `role_permission.py`                                                                                              | `RolePermission`                                                                              |
| `crud_role.py`                           | `role.py`                                                                                                         | `Role`                                                                                        |
| `crud_user.py`                           | `user.py`                                                                                                         | `User`                                                                                        |
| `crud_animal.py`                           | `animal.py`                                                                                                         | `Animal`                                                                                        |
| `crud_body_condition_score.py`                           | `body_condition_score.py`                                                                                                         | `BodyConditioningScore`                                                                                        |

## Example

This example is taken from `crud_user.py`.

Assuming we want to create a `get_by_name` query, but instead of a user id it's the user's email: `get_by_email`. We know that we need the `User` model, as it contains the user's email. The query should return the `User` model, as that is what we are querying for. The method should look something like this:

```python
# crud/crud_user.py

from app import models


def get_by_email(db: Session, *, email: str, record_type) -> models.User | None:
    return (
        db.query(models.User)
        .filter(
            models.User.email == email
        )
        .first()
    )
```

Now we need to encapsulate this inside a crud class. As we mentioned, we need inherit the `CRUDBase` class, which requires us to provide `ModelType`, `CreateSchemaType`, and `UpdateSchemaType`. We know that we want to get the `User` model when we query, therefore `ModelType` is `User`.

`CreateSchemaType` and `UpdateSchemaType` are `UserInvite` and `UserUpdate` respectively. Those are the Pydantic models that will validate the argument for `create` and `update` methods. This is the same as saying _you can only create `User` if the object you provided have the same schema as `CreateSchemaType`_.

The user crud class should look something like this:

```python
# crud/crud_user.py

from app.crud.base import CRUDBase
from app.schemas import (
    UserInvite,
    UserUpdate,
)
from app import models


class CRUDUser(CRUDBase[User, UserInvite, UserUpdate]):

    def get_by_email(self, db: Session, *, email: str, record_type) -> models.User | None:

        ...


user = CRUDUser(models.User)
```

Then we can import this in `__init__.py` to make it easier for the rest of the application to import:

```python
# crud/__init__.py

from .crud_user import user
```

We can use this method inside the fastapi endpoint method, as that is the place where we can have access to the SQLAlchemy database session. _(The session is injected using the `get_db` injection method in `deps.py`)._ If we want to have an endpoint where we can get the user's details by email, our code will look something like this:

```python
# endpoints/user.py

from app import crud, models, schemas, responses
from app.api import deps
from app.scopes import UserScopes


@router.get("/get/{user_email}", response_model=schemas.UserOut, status_code=status.HTTP_200_OK)
def user_get(
    *,
    db: Session = Depends(deps.get_db),
    user_email: str,
    current_user: models.User = Security(
        deps.get_current_user, scopes=[UserScopes.read]
    ),
):
    try:
        query_result = crud.user.get_by_email(db, user_email)

    ...
```
