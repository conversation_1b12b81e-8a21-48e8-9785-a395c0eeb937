"""
This file contains the model mapping of Activity
"""

from datetime import datetime, timezone

from app.db.base_class import Base
# from sqlalchemy.orm import relationship

from sqlalchemy import (
    Column,
    DateTime,
    String,
    Integer,
    ForeignKey,
    JSON
)
    

class ActivityProgress(Base):
    user_id = Column(
        Integer, ForeignKey("user.id", ondelete="RESTRICT"), nullable=False, index=True
    )
    course_id = Column(
        Integer, ForeignKey("course.id", ondelete="RESTRICT"), nullable=False, index=True
    )
    activity_id = Column(
        Integer, ForeignKey("activity.id", ondelete="RESTRICT"), nullable=False, index=True
    )
    user_input = Column(JSON)
    result = Column(JSON)
    retries = Column(Integer, default=0)
    # courses = relationship('Course', secondary=course_activity_association, back_populates='activities')
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                        onupdate=lambda: datetime.now(timezone.utc), nullable=False)